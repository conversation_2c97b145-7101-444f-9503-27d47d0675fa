<?php
require_once 'config.php';

// Global PDO bağlantısını kullan
global $pdo;

// Input validation fonksiyonları
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function validatePhone($phone) {
    return preg_match('/^[0-9+\-\s\(\)]{10,20}$/', $phone);
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// CSRF token oluştur
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// CSRF token doğrula
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Admin giriş kontrolü
function isAdminLoggedIn() {
    return isset($_SESSION[ADMIN_SESSION_NAME]) && $_SESSION[ADMIN_SESSION_NAME] === true;
}

// Admin girişi yap
function adminLogin($password) {
    if ($password === ADMIN_PASSWORD) {
        $_SESSION[ADMIN_SESSION_NAME] = true;
        return true;
    }
    return false;
}

// Admin çıkışı yap
function adminLogout() {
    unset($_SESSION[ADMIN_SESSION_NAME]);
    session_destroy();
}

// Müşteri ekle
function addCustomer($data) {
    global $pdo;

    // Input validation
    if (empty($data['name']) || empty($data['email']) || empty($data['phone'])) {
        return ['error' => 'Tüm alanlar zorunludur.'];
    }

    if (!validateEmail($data['email'])) {
        return ['error' => 'Geçersiz e-posta adresi.'];
    }

    if (!validatePhone($data['phone'])) {
        return ['error' => 'Geçersiz telefon numarası.'];
    }

    try {
        // E-posta benzersizlik kontrolü
        $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
        $stmt->execute([$data['email']]);
        if ($stmt->fetch()) {
            return ['error' => 'Bu e-posta adresi zaten kayıtlı.'];
        }

        $stmt = $pdo->prepare("INSERT INTO customers (name, email, phone, status) VALUES (?, ?, ?, 'active')");
        $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['email']),
            sanitizeInput($data['phone'])
        ]);

        $customerId = $pdo->lastInsertId();
        return getCustomer($customerId);
    } catch (PDOException $e) {
        error_log("Müşteri ekleme hatası: " . $e->getMessage());
        return ['error' => 'Veritabanı hatası oluştu.'];
    }
}

// Müşteri güncelle
function updateCustomer($id, $data) {
    global $pdo;

    // Input validation
    if (empty($data['name']) || empty($data['email']) || empty($data['phone'])) {
        return ['error' => 'Tüm alanlar zorunludur.'];
    }

    if (!validateEmail($data['email'])) {
        return ['error' => 'Geçersiz e-posta adresi.'];
    }

    if (!validatePhone($data['phone'])) {
        return ['error' => 'Geçersiz telefon numarası.'];
    }

    if (!in_array($data['status'], ['active', 'inactive'])) {
        return ['error' => 'Geçersiz durum.'];
    }

    try {
        // E-posta benzersizlik kontrolü (kendisi hariç)
        $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ? AND id != ?");
        $stmt->execute([$data['email'], $id]);
        if ($stmt->fetch()) {
            return ['error' => 'Bu e-posta adresi başka bir müşteri tarafından kullanılıyor.'];
        }

        $stmt = $pdo->prepare("UPDATE customers SET name = ?, email = ?, phone = ?, status = ? WHERE id = ?");
        $result = $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['email']),
            sanitizeInput($data['phone']),
            $data['status'],
            $id
        ]);

        return $result ? true : ['error' => 'Güncelleme başarısız.'];
    } catch (PDOException $e) {
        error_log("Müşteri güncelleme hatası: " . $e->getMessage());
        return ['error' => 'Veritabanı hatası oluştu.'];
    }
}

// Müşteri sil
function deleteCustomer($id) {
    global $pdo;

    try {
        // Foreign key constraints sayesinde ilgili kayıtlar otomatik silinecek
        $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        error_log("Müşteri silme hatası: " . $e->getMessage());
        return false;
    }
}

// Müşteri bul
function getCustomer($id) {
    global $pdo;

    if (!is_numeric($id) || $id <= 0) {
        return null;
    }

    try {
        $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Müşteri getirme hatası: " . $e->getMessage());
        return null;
    }
}

// Tüm müşterileri getir
function getAllCustomers() {
    global $pdo;

    try {
        $stmt = $pdo->query("SELECT * FROM customers ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Müşteri listesi getirme hatası: " . $e->getMessage());
        return [];
    }
}

// Abonelik planlarını getir
function getAllSubscriptions() {
    global $pdo;

    try {
        $stmt = $pdo->query("SELECT * FROM subscriptions ORDER BY created_at ASC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Abonelik listesi getirme hatası: " . $e->getMessage());
        return [];
    }
}

// Müşteri aboneliği ekle
function addCustomerSubscription($customerId, $subscriptionId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("INSERT INTO customer_subscriptions (customer_id, subscription_id, status, next_payment_date) VALUES (?, ?, 'pending', DATE_ADD(NOW(), INTERVAL 1 MONTH))");
        $stmt->execute([$customerId, $subscriptionId]);

        $subscriptionId = $pdo->lastInsertId();
        return getCustomerSubscription($subscriptionId);
    } catch (PDOException $e) {
        error_log("Müşteri aboneliği ekleme hatası: " . $e->getMessage());
        return false;
    }
}

// Müşteri aboneliği getir
function getCustomerSubscription($id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT * FROM customer_subscriptions WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Müşteri aboneliği getirme hatası: " . $e->getMessage());
        return null;
    }
}

// Tüm müşteri aboneliklerini getir
function getAllCustomerSubscriptions() {
    global $pdo;

    try {
        $stmt = $pdo->query("SELECT * FROM customer_subscriptions ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Müşteri abonelik listesi getirme hatası: " . $e->getMessage());
        return [];
    }
}

// Ödeme kaydı ekle
function addPayment($data) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("INSERT INTO payments (customer_id, subscription_id, amount, status, paytr_transaction_id) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            $data['customer_id'],
            $data['subscription_id'],
            $data['amount'],
            $data['status'],
            $data['paytr_transaction_id'] ?? null
        ]);

        $paymentId = $pdo->lastInsertId();
        return getPayment($paymentId);
    } catch (PDOException $e) {
        error_log("Ödeme ekleme hatası: " . $e->getMessage());
        return false;
    }
}

// Ödeme getir
function getPayment($id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT * FROM payments WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Ödeme getirme hatası: " . $e->getMessage());
        return null;
    }
}

// Tüm ödemeleri getir
function getAllPayments() {
    global $pdo;

    try {
        $stmt = $pdo->query("SELECT * FROM payments ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Ödeme listesi getirme hatası: " . $e->getMessage());
        return [];
    }
}

// Kart bilgisi ekle
function addPaymentCard($data) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("INSERT INTO payment_cards (customer_id, card_token, card_mask, card_type) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE card_mask = VALUES(card_mask), card_type = VALUES(card_type), updated_at = NOW()");
        return $stmt->execute([
            $data['customer_id'],
            $data['card_token'],
            $data['card_mask'] ?? '',
            $data['card_type'] ?? ''
        ]);
    } catch (PDOException $e) {
        error_log("Kart bilgisi ekleme hatası: " . $e->getMessage());
        return false;
    }
}

// Müşterinin kayıtlı kartını getir
function getCustomerCard($customerId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT * FROM payment_cards WHERE customer_id = ? AND is_active = 1 ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$customerId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Müşteri kartı getirme hatası: " . $e->getMessage());
        return null;
    }
}

// PayTR log ekle
function addPayTRLog($data) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("INSERT INTO paytr_logs (merchant_oid, status, total_amount, post_data) VALUES (?, ?, ?, ?)");
        return $stmt->execute([
            $data['merchant_oid'],
            $data['status'],
            $data['total_amount'] ?? null,
            json_encode($data['post_data'] ?? [])
        ]);
    } catch (PDOException $e) {
        error_log("PayTR log ekleme hatası: " . $e->getMessage());
        return false;
    }
}

// Aylık ödeme log ekle
function addMonthlyPaymentLog($data) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("INSERT INTO monthly_payment_logs (processed_count, success_count, failed_count, details) VALUES (?, ?, ?, ?)");
        return $stmt->execute([
            $data['processed'],
            $data['success'],
            $data['failed'],
            $data['details']
        ]);
    } catch (PDOException $e) {
        error_log("Aylık ödeme log ekleme hatası: " . $e->getMessage());
        return false;
    }
}

// Abonelik iptal et
function cancelSubscription($customerSubscriptionId, $reason = 'user_request') {
    global $pdo;

    try {
        $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'cancelled', cancelled_at = NOW(), cancellation_reason = ? WHERE id = ?");
        return $stmt->execute([$reason, $customerSubscriptionId]);
    } catch (PDOException $e) {
        error_log("Abonelik iptal hatası: " . $e->getMessage());
        return false;
    }
}

// Abonelik duraklat
function pauseSubscription($customerSubscriptionId, $pauseUntil = null) {
    global $pdo;

    try {
        $pauseUntilDate = $pauseUntil ? $pauseUntil : date('Y-m-d', strtotime('+1 month'));
        $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'paused', paused_until = ? WHERE id = ?");
        return $stmt->execute([$pauseUntilDate, $customerSubscriptionId]);
    } catch (PDOException $e) {
        error_log("Abonelik duraklat hatası: " . $e->getMessage());
        return false;
    }
}

// Abonelik devam ettir
function resumeSubscription($customerSubscriptionId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'active', paused_until = NULL WHERE id = ?");
        return $stmt->execute([$customerSubscriptionId]);
    } catch (PDOException $e) {
        error_log("Abonelik devam ettir hatası: " . $e->getMessage());
        return false;
    }
}

// Abonelik planı değiştir
function changeSubscriptionPlan($customerSubscriptionId, $newSubscriptionId, $prorationMode = 'immediate') {
    global $pdo;

    try {
        $pdo->beginTransaction();

        // Mevcut aboneliği al
        $stmt = $pdo->prepare("SELECT * FROM customer_subscriptions WHERE id = ?");
        $stmt->execute([$customerSubscriptionId]);
        $currentSub = $stmt->fetch();

        if (!$currentSub) {
            throw new Exception('Abonelik bulunamadı');
        }

        // Yeni abonelik planını al
        $stmt = $pdo->prepare("SELECT * FROM subscriptions WHERE id = ?");
        $stmt->execute([$newSubscriptionId]);
        $newPlan = $stmt->fetch();

        if (!$newPlan) {
            throw new Exception('Yeni abonelik planı bulunamadı');
        }

        // Proration hesaplama
        $prorationAmount = 0;
        if ($prorationMode === 'immediate') {
            // Mevcut dönemin kalan günleri için hesaplama
            $currentDate = new DateTime();
            $nextPaymentDate = new DateTime($currentSub['next_payment_date']);
            $daysRemaining = $currentDate->diff($nextPaymentDate)->days;
            $daysInMonth = 30; // Ortalama

            // Mevcut planın günlük maliyeti
            $stmt = $pdo->prepare("SELECT price FROM subscriptions WHERE id = ?");
            $stmt->execute([$currentSub['subscription_id']]);
            $currentPrice = $stmt->fetchColumn();

            $dailyCurrentCost = $currentPrice / $daysInMonth;
            $dailyNewCost = $newPlan['price'] / $daysInMonth;

            $prorationAmount = ($dailyNewCost - $dailyCurrentCost) * $daysRemaining;
        }

        // Aboneliği güncelle
        $stmt = $pdo->prepare("UPDATE customer_subscriptions SET subscription_id = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$newSubscriptionId, $customerSubscriptionId]);

        // Proration ödemesi gerekiyorsa kaydet
        if ($prorationAmount > 0) {
            addPayment([
                'customer_id' => $currentSub['customer_id'],
                'subscription_id' => $newSubscriptionId,
                'amount' => $prorationAmount,
                'status' => 'pending',
                'paytr_transaction_id' => 'PRORATION_' . time()
            ]);
        }

        $pdo->commit();
        return ['success' => true, 'proration_amount' => $prorationAmount];

    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Abonelik planı değiştir hatası: " . $e->getMessage());
        return ['error' => $e->getMessage()];
    }
}

// Müşterinin aktif aboneliklerini getir
function getCustomerActiveSubscriptions($customerId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT cs.*, s.name as subscription_name, s.price, s.description
            FROM customer_subscriptions cs
            JOIN subscriptions s ON cs.subscription_id = s.id
            WHERE cs.customer_id = ? AND cs.status IN ('active', 'paused')
            ORDER BY cs.created_at DESC
        ");
        $stmt->execute([$customerId]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Müşteri abonelik getirme hatası: " . $e->getMessage());
        return [];
    }
}

// Ödeme geçmişi getir
function getPaymentHistory($customerId, $limit = 50) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT p.*, s.name as subscription_name
            FROM payments p
            JOIN subscriptions s ON p.subscription_id = s.id
            WHERE p.customer_id = ?
            ORDER BY p.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$customerId, $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Ödeme geçmişi getirme hatası: " . $e->getMessage());
        return [];
    }
}

// Abonelik istatistikleri
function getSubscriptionStats() {
    global $pdo;

    try {
        $stats = [];

        // Toplam aktif abonelik
        $stmt = $pdo->query("SELECT COUNT(*) FROM customer_subscriptions WHERE status = 'active'");
        $stats['active_subscriptions'] = $stmt->fetchColumn();

        // Toplam gelir (bu ay)
        $stmt = $pdo->query("SELECT SUM(amount) FROM payments WHERE status = 'success' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
        $stats['monthly_revenue'] = $stmt->fetchColumn() ?: 0;

        // Churn rate (bu ay iptal olan / toplam aktif)
        $stmt = $pdo->query("SELECT COUNT(*) FROM customer_subscriptions WHERE status = 'cancelled' AND MONTH(cancelled_at) = MONTH(NOW()) AND YEAR(cancelled_at) = YEAR(NOW())");
        $cancelledThisMonth = $stmt->fetchColumn();
        $stats['churn_rate'] = $stats['active_subscriptions'] > 0 ? ($cancelledThisMonth / $stats['active_subscriptions']) * 100 : 0;

        // Başarısız ödeme oranı
        $stmt = $pdo->query("SELECT COUNT(*) FROM payments WHERE status = 'failed' AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $failedPayments = $stmt->fetchColumn();
        $stmt = $pdo->query("SELECT COUNT(*) FROM payments WHERE DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $totalPayments = $stmt->fetchColumn();
        $stats['failed_payment_rate'] = $totalPayments > 0 ? ($failedPayments / $totalPayments) * 100 : 0;

        return $stats;
    } catch (PDOException $e) {
        error_log("İstatistik getirme hatası: " . $e->getMessage());
        return [];
    }
}

// Müşterinin PayTR utoken'ını getir
function getCustomerUtoken($customerId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT utoken FROM payment_cards WHERE customer_id = ? AND is_active = 1 AND utoken IS NOT NULL ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$customerId]);

        $result = $stmt->fetch();
        return $result ? $result['utoken'] : null;
    } catch (PDOException $e) {
        error_log("Müşteri utoken getirme hatası: " . $e->getMessage());
        return null;
    }
}

// Abonelik planı getir
function getSubscription($id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("SELECT * FROM subscriptions WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Abonelik getirme hatası: " . $e->getMessage());
        return null;
    }
}
?>
