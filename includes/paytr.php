<?php
require_once 'config.php';
require_once 'functions.php';

class PayTR {

    // Debug fonksiyonu
    public static function debugLog($message, $data = null) {
        if (PAYTR_TEST_MODE) {
            $logMessage = "[PayTR DEBUG] " . $message;
            if ($data !== null) {
                $logMessage .= " | Data: " . print_r($data, true);
            }
            error_log($logMessage);

            // Ayrıca dosyaya da yaz
            $logFile = __DIR__ . '/../logs/paytr_debug.log';
            $timestamp = date('Y-m-d H:i:s');
            file_put_contents($logFile, "[$timestamp] $logMessage\n", FILE_APPEND | LOCK_EX);
        }
    }

    // Ödeme formu oluştur
    public static function createPaymentForm($customerId, $subscriptionId, $amount, $customerInfo) {
        // Input validation
        if (!is_numeric($customerId) || $customerId <= 0) {
            throw new InvalidArgumentException('Geçersiz müşteri ID');
        }

        if (!is_numeric($subscriptionId) || $subscriptionId <= 0) {
            throw new InvalidArgumentException('Geçersiz abonelik ID');
        }

        if (!is_numeric($amount) || $amount <= 0) {
            throw new InvalidArgumentException('Geçersiz tutar');
        }

        if (empty($customerInfo['email']) || !filter_var($customerInfo['email'], FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException('Geçersiz e-posta adresi');
        }

        $merchant_id = PAYTR_MERCHANT_ID;
        $merchant_key = PAYTR_MERCHANT_KEY;
        $merchant_salt = PAYTR_MERCHANT_SALT;
        
        // Sipariş bilgileri
        $merchant_oid = "SUB" . $customerId . "S" . $subscriptionId . "T" . time();
        $email = $customerInfo['email'];
        $payment_amount = $amount * 100; // PayTR kuruş cinsinden bekler
        $currency = "TL";
        $test_mode = PAYTR_TEST_MODE ? "1" : "0";
        
        // Kullanıcı bilgileri
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $user_name = $customerInfo['name'];
        $user_address = "Adres bilgisi";
        $user_phone = $customerInfo['phone'];

        // Taksit bilgileri
        $no_installment = "1"; // Taksit yok
        $max_installment = "0"; // Maksimum taksit sayısı

        // Sepet bilgileri (test sayfasındaki exact format)
        $user_basket = base64_encode(json_encode([
            ["Abonelik Ödemesi", number_format($amount, 2), 1]
        ]));

        // Callback ve return URL'leri
        $merchant_ok_url = SITE_URL . "/payment_success.php";
        $merchant_fail_url = SITE_URL . "/payment_failed.php";
        
        // Hash oluştur (PayTR dokümantasyonuna göre)
        $paytr_token = $merchant_id . $user_ip . $merchant_oid . $email . $payment_amount . $user_basket . $no_installment . $max_installment . $currency . $test_mode;
        $token = base64_encode(hash_hmac('sha256', $paytr_token . $merchant_salt, $merchant_key, true));

        // Debug log
        self::debugLog("createPaymentForm - Hash hesaplama", [
            'merchant_id' => $merchant_id,
            'user_ip' => $user_ip,
            'merchant_oid' => $merchant_oid,
            'email' => $email,
            'payment_amount' => $payment_amount,
            'user_basket' => $user_basket,
            'no_installment' => $no_installment,
            'max_installment' => $max_installment,
            'currency' => $currency,
            'test_mode' => $test_mode,
            'paytr_token_string' => $paytr_token,
            'generated_token' => $token
        ]);
        
        // Müşterinin mevcut utoken'ını kontrol et
        $existingUtoken = getCustomerUtoken($customerId);

        // POST verileri
        $post_vals = [
            'merchant_id' => $merchant_id,
            'user_ip' => $user_ip,
            'merchant_oid' => $merchant_oid,
            'email' => $email,
            'payment_amount' => $payment_amount,
            'payment_type' => 'card', // Zorunlu alan
            'currency' => $currency,
            'user_name' => $user_name,
            'user_address' => $user_address,
            'user_phone' => $user_phone,
            'merchant_ok_url' => $merchant_ok_url,
            'merchant_fail_url' => $merchant_fail_url,
            'user_basket' => $user_basket,
            'paytr_token' => $token,
            'test_mode' => $test_mode,
            'no_installment' => $no_installment,
            'max_installment' => $max_installment,
            'store_card' => "1", // PayTR'nin doğru parametresi
            'non_3d' => "0"
        ];

        // Eğer müşterinin daha önce kartı varsa utoken ekle
        if ($existingUtoken) {
            $post_vals['utoken'] = $existingUtoken;
        }

        self::debugLog("createPaymentForm - POST verileri", $post_vals);

        // PayTR'den token al
        $tokenResponse = self::getPaymentToken($post_vals);

        if (isset($tokenResponse['status']) && $tokenResponse['status'] === 'success') {
            return [
                'status' => 'success',
                'token' => $tokenResponse['token'],
                'form_data' => $post_vals
            ];
        } else {
            return [
                'status' => 'error',
                'error' => $tokenResponse['reason'] ?? 'Token alınamadı',
                'form_data' => $post_vals
            ];
        }
    }

    // PayTR'den token al
    public static function getPaymentToken($post_vals) {
        self::debugLog("getPaymentToken - API çağrısı başlıyor", $post_vals);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://www.paytr.com/odeme/api/get-token");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_vals));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_USERAGENT, 'PayTR-Abonelik-Sistemi/1.0');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        self::debugLog("getPaymentToken - API yanıtı", [
            'http_code' => $httpCode,
            'curl_error' => $curlError,
            'response' => $result
        ]);

        if ($result !== false && $httpCode === 200) {
            $decodedResult = json_decode($result, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decodedResult;
            } else {
                return ['status' => 'error', 'reason' => 'JSON decode hatası: ' . json_last_error_msg()];
            }
        } else {
            return ['status' => 'error', 'reason' => "API hatası: HTTP $httpCode, cURL: $curlError"];
        }
    }

    // Kart kaydetme formu oluştur
    public static function createCardSaveForm($customerId, $customerInfo) {
        // Input validation
        if (!is_numeric($customerId) || $customerId <= 0) {
            throw new InvalidArgumentException('Geçersiz müşteri ID');
        }

        if (empty($customerInfo['email']) || !filter_var($customerInfo['email'], FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException('Geçersiz e-posta adresi');
        }

        $merchant_id = PAYTR_MERCHANT_ID;
        $merchant_key = PAYTR_MERCHANT_KEY;
        $merchant_salt = PAYTR_MERCHANT_SALT;
        
        $email = $customerInfo['email'];
        $user_name = $customerInfo['name'];
        $user_phone = $customerInfo['phone'];
        
        // Kart kaydetme için özel merchant_oid
        $merchant_oid = "CARD" . $customerId . "T" . time();
        
        // Callback URL
        $merchant_ok_url = SITE_URL . "/payment_success.php";
        $merchant_fail_url = SITE_URL . "/payment_failed.php";
        
        // Kart kaydetme için gerekli değişkenler
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $payment_amount = 100; // 1 TL test ödemesi (kuruş cinsinden)
        $user_basket = base64_encode(json_encode([["Kart Kaydetme", "1.00", 1]]));
        $no_installment = "1";
        $max_installment = "0";
        $currency = "TL";
        $test_mode = PAYTR_TEST_MODE ? "1" : "0";

        // Hash oluştur (test sayfasındaki gibi)
        $paytr_token = $merchant_id . $user_ip . $merchant_oid . $email . $payment_amount . $user_basket . $no_installment . $max_installment . $currency . $test_mode;
        $token = base64_encode(hash_hmac('sha256', $paytr_token . $merchant_salt, $merchant_key, true));

        self::debugLog("createCardSaveForm - Hash hesaplama", [
            'merchant_id' => $merchant_id,
            'user_ip' => $user_ip,
            'merchant_oid' => $merchant_oid,
            'email' => $email,
            'payment_amount' => $payment_amount,
            'user_basket' => $user_basket,
            'no_installment' => $no_installment,
            'max_installment' => $max_installment,
            'currency' => $currency,
            'test_mode' => $test_mode,
            'paytr_token_string' => $paytr_token,
            'generated_token' => $token
        ]);
        
        $post_vals = [
            'merchant_id' => $merchant_id,
            'user_ip' => $user_ip,
            'merchant_oid' => $merchant_oid,
            'email' => $email,
            'payment_amount' => $payment_amount,
            'payment_type' => 'card', // Zorunlu alan
            'currency' => $currency,
            'user_name' => $user_name,
            'user_address' => "Kart kaydetme",
            'user_phone' => $user_phone,
            'merchant_ok_url' => $merchant_ok_url,
            'merchant_fail_url' => $merchant_fail_url,
            'user_basket' => $user_basket,
            'paytr_token' => $token,
            'test_mode' => $test_mode,
            'no_installment' => $no_installment,
            'max_installment' => $max_installment,
            'card_save' => "1", // Kart kaydetme aktif
            'non_3d' => "1" // Non-3D aktif (otomatik ödeme için gerekli)
        ];

        self::debugLog("createCardSaveForm - POST verileri", $post_vals);

        // PayTR'den token al
        $tokenResponse = self::getPaymentToken($post_vals);

        if (isset($tokenResponse['status']) && $tokenResponse['status'] === 'success') {
            return [
                'status' => 'success',
                'token' => $tokenResponse['token'],
                'form_data' => $post_vals
            ];
        } else {
            return [
                'status' => 'error',
                'error' => $tokenResponse['reason'] ?? 'Token alınamadı',
                'form_data' => $post_vals
            ];
        }
    }
    
    // Kayıtlı kart ile ödeme
    public static function chargeCard($cardToken, $customerId, $subscriptionId, $amount) {
        $merchant_id = PAYTR_MERCHANT_ID;
        $merchant_key = PAYTR_MERCHANT_KEY;
        $merchant_salt = PAYTR_MERCHANT_SALT;

        $merchant_oid = "AUTO" . $customerId . "S" . $subscriptionId . "T" . time();
        $payment_amount = $amount * 100; // Kuruş cinsinden
        $currency = "TL";
        $test_mode = PAYTR_TEST_MODE ? "1" : "0";

        // PayTR kayıtlı kart ödeme hash'i (farklı sıralama)
        $paytr_token = $merchant_id . $merchant_oid . $payment_amount . $currency . $cardToken . $test_mode;
        $token = base64_encode(hash_hmac('sha256', $paytr_token . $merchant_salt, $merchant_key, true));

        self::debugLog("chargeCard - Hash hesaplama", [
            'merchant_id' => $merchant_id,
            'merchant_oid' => $merchant_oid,
            'payment_amount' => $payment_amount,
            'currency' => $currency,
            'card_token' => $cardToken,
            'test_mode' => $test_mode,
            'paytr_token_string' => $paytr_token,
            'generated_token' => $token
        ]);

        $post_vals = [
            'merchant_id' => $merchant_id,
            'merchant_oid' => $merchant_oid,
            'payment_amount' => $payment_amount,
            'currency' => $currency,
            'card_token' => $cardToken,
            'paytr_token' => $token,
            'test_mode' => $test_mode
        ];
        
        self::debugLog("chargeCard - POST verileri", $post_vals);

        // PayTR API'ye istek gönder (retry mechanism ile)
        $maxRetries = 3;
        $retryDelay = 1; // saniye

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://www.paytr.com/odeme/api/card-payment");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_vals));
            curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Test için geçici
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0); // Test için geçici
            curl_setopt($ch, CURLOPT_USERAGENT, 'PayTR-Abonelik-Sistemi/1.0');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded',
                'Accept: application/json'
            ]);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            self::debugLog("chargeCard - API yanıtı", [
                'attempt' => $attempt,
                'http_code' => $httpCode,
                'curl_error' => $curlError,
                'response' => $result
            ]);

            if ($result !== false && $httpCode === 200) {
                $decodedResult = json_decode($result, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    self::debugLog("chargeCard - Başarılı yanıt", $decodedResult);
                    return $decodedResult;
                } else {
                    self::debugLog("chargeCard - JSON decode hatası", [
                        'error' => json_last_error_msg(),
                        'raw_response' => $result
                    ]);
                }
            } else {
                self::debugLog("chargeCard - API hatası", [
                    'attempt' => $attempt,
                    'http_code' => $httpCode,
                    'curl_error' => $curlError,
                    'response_body' => $result
                ]);
            }

            if ($attempt < $maxRetries) {
                sleep($retryDelay);
                $retryDelay *= 2; // Exponential backoff
            }
        }

        return ['status' => 'error', 'error_message' => 'PayTR API connection failed after ' . $maxRetries . ' attempts'];
    }
    
    // Callback doğrulama
    public static function verifyCallback($post_data) {
        $merchant_key = PAYTR_MERCHANT_KEY;
        $merchant_salt = PAYTR_MERCHANT_SALT;

        if (!isset($post_data['merchant_oid'], $post_data['status'], $post_data['total_amount'], $post_data['hash'])) {
            return false;
        }

        $hash = base64_encode(hash_hmac('sha256', $post_data['merchant_oid'] . $merchant_salt . $post_data['status'] . $post_data['total_amount'], $merchant_key, true));

        return hash_equals($hash, $post_data['hash']);
    }

    // Ödeme durumu sorgulama
    public static function queryPayment($merchant_oid) {
        $merchant_id = PAYTR_MERCHANT_ID;
        $merchant_key = PAYTR_MERCHANT_KEY;
        $merchant_salt = PAYTR_MERCHANT_SALT;

        // Hash oluştur
        $paytr_token = $merchant_id . $merchant_oid;
        $token = base64_encode(hash_hmac('sha256', $paytr_token . $merchant_salt, $merchant_key, true));

        $post_vals = [
            'merchant_id' => $merchant_id,
            'merchant_oid' => $merchant_oid,
            'paytr_token' => $token
        ];

        return self::makeApiRequest('https://www.paytr.com/odeme/api/query', $post_vals);
    }

    // Kart silme
    public static function deleteCard($cardToken) {
        $merchant_id = PAYTR_MERCHANT_ID;
        $merchant_key = PAYTR_MERCHANT_KEY;
        $merchant_salt = PAYTR_MERCHANT_SALT;

        // Hash oluştur
        $paytr_token = $merchant_id . $cardToken;
        $token = base64_encode(hash_hmac('sha256', $paytr_token . $merchant_salt, $merchant_key, true));

        $post_vals = [
            'merchant_id' => $merchant_id,
            'card_token' => $cardToken,
            'paytr_token' => $token
        ];

        return self::makeApiRequest('https://www.paytr.com/odeme/api/delete-card', $post_vals);
    }

    // İade işlemi
    public static function refundPayment($merchant_oid, $amount = null) {
        $merchant_id = PAYTR_MERCHANT_ID;
        $merchant_key = PAYTR_MERCHANT_KEY;
        $merchant_salt = PAYTR_MERCHANT_SALT;

        // Hash oluştur
        $paytr_token = $merchant_id . $merchant_oid . ($amount ? $amount * 100 : '');
        $token = base64_encode(hash_hmac('sha256', $paytr_token . $merchant_salt, $merchant_key, true));

        $post_vals = [
            'merchant_id' => $merchant_id,
            'merchant_oid' => $merchant_oid,
            'paytr_token' => $token
        ];

        if ($amount) {
            $post_vals['refund_amount'] = $amount * 100; // Kuruş cinsinden
        }

        return self::makeApiRequest('https://www.paytr.com/odeme/api/refund', $post_vals);
    }

    // Genel API istek fonksiyonu
    private static function makeApiRequest($url, $post_vals, $maxRetries = 3) {
        $retryDelay = 1;

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_vals));
            curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
            curl_setopt($ch, CURLOPT_USERAGENT, 'PayTR-Abonelik-Sistemi/1.0');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded',
                'Accept: application/json'
            ]);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($result !== false && $httpCode === 200) {
                $decodedResult = json_decode($result, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $decodedResult;
                } else {
                    error_log("PayTR API JSON decode error: " . json_last_error_msg());
                }
            } else {
                error_log("PayTR API error (attempt $attempt): HTTP $httpCode, cURL: $curlError, URL: $url");
            }

            if ($attempt < $maxRetries) {
                sleep($retryDelay);
                $retryDelay *= 2;
            }
        }

        return ['status' => 'error', 'error_message' => 'API connection failed after ' . $maxRetries . ' attempts'];
    }
}
?>
