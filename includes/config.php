<?php
// PayTR API Ayarları
define('PAYTR_MERCHANT_ID', '583632');
define('PAYTR_MERCHANT_KEY', 'PmyLFkiAJ1Pb5L57');
define('PAYTR_MERCHANT_SALT', '5kzcMRjcwj5ErwS4');
define('PAYTR_TEST_MODE', true); // Test modu için true, canlı için false

// Veritabanı Ayarları
define('DB_HOST', 'localhost');
define('DB_NAME', 'admin_intdenetim');
define('DB_USER', 'intdenetim');
define('DB_PASS', '!Rg2KX3yoz?qnny1');
define('DB_CHARSET', 'utf8mb4');

// Admin Panel Ayarları
define('ADMIN_PASSWORD', 'admin123'); // Admin panel şifresi (basit mod)
define('ADMIN_SESSION_NAME', 'paytr_admin_logged_in');

// Site Ayarları
define('SITE_URL', 'https://abonelik.ozmdirect.net/int');
define('SITE_NAME', 'PayTR Abonelik Sistemi');

// Timezone ayarı
date_default_timezone_set('Europe/Istanbul');

// Error reporting ayarları
if (PAYTR_TEST_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/../logs/error.log');
}

// Global error handler
function globalErrorHandler($errno, $errstr, $errfile, $errline) {
    $error = "Error: [$errno] $errstr in $errfile on line $errline";
    error_log($error);

    if (!PAYTR_TEST_MODE) {
        header('Location: /error.php?code=500&message=' . urlencode('Sistem hatası oluştu.'));
        exit;
    }

    return false;
}

// Global exception handler
function globalExceptionHandler($exception) {
    $error = "Uncaught exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();
    error_log($error);

    if (!PAYTR_TEST_MODE) {
        header('Location: /error.php?code=500&message=' . urlencode('Sistem hatası oluştu.'));
        exit;
    }
}

set_error_handler('globalErrorHandler');
set_exception_handler('globalExceptionHandler');

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();

    // Session güvenliği
    if (!isset($_SESSION['initiated'])) {
        session_regenerate_id(true);
        $_SESSION['initiated'] = true;
    }

    // Session timeout (2 saat)
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 7200)) {
        session_unset();
        session_destroy();
        session_start();
    }
    $_SESSION['last_activity'] = time();
}

// Veritabanı bağlantısı
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 10,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
} catch (PDOException $e) {
    error_log('Veritabanı bağlantı hatası: ' . $e->getMessage());

    if (PAYTR_TEST_MODE) {
        die('Veritabanı bağlantı hatası: ' . $e->getMessage());
    } else {
        header('Location: /error.php?code=500&message=' . urlencode('Veritabanı bağlantısı kurulamadı.'));
        exit;
    }
}
?>
