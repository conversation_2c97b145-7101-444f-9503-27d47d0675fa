<?php
require_once 'includes/config.php';

if (!PAYTR_TEST_MODE) {
    die('Debug sadece test modunda görüntülenebilir!');
}

$logFile = __DIR__ . '/logs/paytr_debug.log';
$errorLogFile = __DIR__ . '/logs/error.log';

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayTR Debug Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .log-content {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <button class="btn btn-primary refresh-btn" onclick="location.reload()">
            <i class="fas fa-sync"></i> Yenile
        </button>
        
        <h2>PayTR Debug Logs</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h5>PayTR Debug Log</h5>
                        <small><?php echo file_exists($logFile) ? 'Son güncelleme: ' . date('d.m.Y H:i:s', filemtime($logFile)) : 'Dosya yok'; ?></small>
                    </div>
                    <div class="card-body">
                        <div class="log-content">
<?php
if (file_exists($logFile)) {
    $content = file_get_contents($logFile);
    if ($content) {
        // Son 50 satırı göster
        $lines = explode("\n", $content);
        $lines = array_slice($lines, -50);
        echo htmlspecialchars(implode("\n", $lines));
    } else {
        echo "Log dosyası boş.";
    }
} else {
    echo "PayTR debug log dosyası bulunamadı.";
}
?>
                        </div>
                        <div class="mt-3">
                            <a href="?clear_debug=1" class="btn btn-sm btn-warning" onclick="return confirm('Debug logları temizlensin mi?')">
                                Temizle
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h5>Error Log</h5>
                        <small><?php echo file_exists($errorLogFile) ? 'Son güncelleme: ' . date('d.m.Y H:i:s', filemtime($errorLogFile)) : 'Dosya yok'; ?></small>
                    </div>
                    <div class="card-body">
                        <div class="log-content">
<?php
if (file_exists($errorLogFile)) {
    $content = file_get_contents($errorLogFile);
    if ($content) {
        // Son 50 satırı göster
        $lines = explode("\n", $content);
        $lines = array_slice($lines, -50);
        echo htmlspecialchars(implode("\n", $lines));
    } else {
        echo "Error log dosyası boş.";
    }
} else {
    echo "Error log dosyası bulunamadı.";
}
?>
                        </div>
                        <div class="mt-3">
                            <a href="?clear_error=1" class="btn btn-sm btn-warning" onclick="return confirm('Error logları temizlensin mi?')">
                                Temizle
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>PayTR Test</h5>
                    </div>
                    <div class="card-body">
                        <p>PayTR fonksiyonlarını test etmek için:</p>
                        <div class="d-flex gap-2">
                            <a href="paytr_test.php" class="btn btn-primary" target="_blank">
                                PayTR Test Sayfası
                            </a>
                            <a href="save_card.php?customer_id=1" class="btn btn-success" target="_blank">
                                Kart Kaydetme Test
                            </a>
                            <a href="index.php?customer_id=1&subscription_id=1" class="btn btn-info" target="_blank">
                                Ödeme Test
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="admin/" class="btn btn-secondary">← Admin Panel</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    
    <script>
        // Auto refresh her 10 saniyede bir
        setInterval(function() {
            location.reload();
        }, 10000);
    </script>
</body>
</html>

<?php
// Log temizleme
if (isset($_GET['clear_debug']) && file_exists($logFile)) {
    file_put_contents($logFile, '');
    header('Location: debug_logs.php');
    exit;
}

if (isset($_GET['clear_error']) && file_exists($errorLogFile)) {
    file_put_contents($errorLogFile, '');
    header('Location: debug_logs.php');
    exit;
}
?>
