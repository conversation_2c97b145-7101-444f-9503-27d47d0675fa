<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// URL parametrelerinden bilgileri al
$merchant_oid = $_GET['merchant_oid'] ?? '';
$failed_reason_code = $_GET['failed_reason_code'] ?? '';
$failed_reason_msg = $_GET['failed_reason_msg'] ?? '';

// Merchant OID'den bilgileri çıkar
$customerInfo = null;
$subscriptionInfo = null;

if (strpos($merchant_oid, 'SUB') === 0) {
    preg_match('/SUB(\d+)S(\d+)T(\d+)/', $merchant_oid, $matches);
    $customerId = $matches[1] ?? null;
    $subscriptionId = $matches[2] ?? null;
    
    if ($customerId && $subscriptionId) {
        $customerInfo = getCustomer($customerId);
        $subscriptionInfo = getSubscription($subscriptionId);
    }
} elseif (strpos($merchant_oid, 'CARD') === 0) {
    preg_match('/CARD(\d+)T(\d+)/', $merchant_oid, $matches);
    $customerId = $matches[1] ?? null;
    
    if ($customerId) {
        $customerInfo = getCustomer($customerId);
    }
}

// Hata mesajlarını Türkçeleştir
$errorMessages = [
    'INVALID_CARD' => 'Geçersiz kart bilgileri',
    'INSUFFICIENT_FUNDS' => 'Yetersiz bakiye',
    'EXPIRED_CARD' => 'Kartın süresi dolmuş',
    'INVALID_CVC' => 'Geçersiz güvenlik kodu',
    'CARD_NOT_PERMITTED' => 'Kart bu işlem için uygun değil',
    'GENERAL_ERROR' => 'Genel hata oluştu',
    'TIMEOUT' => 'İşlem zaman aşımına uğradı'
];

$friendlyErrorMsg = $errorMessages[$failed_reason_code] ?? $failed_reason_msg ?? 'Bilinmeyen hata';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme Başarısız - PayTR Abonelik Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .failed-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .failed-card {
            background: white;
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .failed-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
        }
        
        .failed-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .failed-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .failed-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .failed-body {
            padding: 3rem 2rem;
        }
        
        .error-card {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 5px solid #ffc107;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
        }
        
        .info-value {
            font-weight: bold;
            color: #495057;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .solutions-card {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .solutions-title {
            color: #0066cc;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .solutions-list {
            list-style: none;
            padding: 0;
        }
        
        .solutions-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #cce7ff;
        }
        
        .solutions-list li:last-child {
            border-bottom: none;
        }
        
        .solutions-list i {
            color: #0066cc;
            margin-right: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .failed-header {
                padding: 2rem 1rem;
            }
            
            .failed-title {
                font-size: 2rem;
            }
            
            .failed-body {
                padding: 2rem 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="failed-container">
        <div class="failed-card animate__animated animate__bounceIn">
            <div class="failed-header">
                <div class="failed-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h1 class="failed-title">Üzgünüz!</h1>
                <p class="failed-subtitle">Ödeme işlemi başarısız oldu</p>
            </div>
            
            <div class="failed-body">
                <div class="error-card">
                    <h5 class="text-warning mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>Hata Detayı
                    </h5>
                    <p class="mb-0"><strong><?php echo htmlspecialchars($friendlyErrorMsg); ?></strong></p>
                    <?php if ($failed_reason_code): ?>
                        <small class="text-muted">Hata Kodu: <?php echo htmlspecialchars($failed_reason_code); ?></small>
                    <?php endif; ?>
                </div>
                
                <?php if ($merchant_oid): ?>
                <div class="info-row">
                    <span class="info-label">
                        <i class="fas fa-receipt me-2"></i>İşlem No
                    </span>
                    <span class="info-value"><?php echo htmlspecialchars($merchant_oid); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if ($customerInfo): ?>
                <div class="info-row">
                    <span class="info-label">
                        <i class="fas fa-user me-2"></i>Müşteri
                    </span>
                    <span class="info-value"><?php echo htmlspecialchars($customerInfo['name']); ?></span>
                </div>
                <?php endif; ?>
                
                <div class="info-row">
                    <span class="info-label">
                        <i class="fas fa-calendar me-2"></i>İşlem Tarihi
                    </span>
                    <span class="info-value"><?php echo date('d.m.Y H:i'); ?></span>
                </div>
                
                <div class="solutions-card">
                    <div class="solutions-title">
                        <i class="fas fa-lightbulb me-2"></i>Çözüm Önerileri
                    </div>
                    <ul class="solutions-list">
                        <li><i class="fas fa-check"></i> Kart bilgilerinizi kontrol edin</li>
                        <li><i class="fas fa-check"></i> Kartınızda yeterli bakiye olduğundan emin olun</li>
                        <li><i class="fas fa-check"></i> Kartınızın son kullanma tarihini kontrol edin</li>
                        <li><i class="fas fa-check"></i> Güvenlik kodunu doğru girdiğinizden emin olun</li>
                        <li><i class="fas fa-check"></i> Farklı bir kart ile deneyebilirsiniz</li>
                        <li><i class="fas fa-check"></i> Bankanız ile iletişime geçebilirsiniz</li>
                    </ul>
                </div>
                
                <div class="action-buttons">
                    <?php if (strpos($merchant_oid, 'SUB') === 0 && $customerInfo && $subscriptionInfo): ?>
                        <a href="index.php?customer_id=<?php echo $customerInfo['id']; ?>&subscription_id=<?php echo $subscriptionInfo['id']; ?>" 
                           class="btn btn-primary btn-custom">
                            <i class="fas fa-redo me-2"></i>Tekrar Dene
                        </a>
                    <?php elseif (strpos($merchant_oid, 'CARD') === 0 && $customerInfo): ?>
                        <a href="save_card.php?customer_id=<?php echo $customerInfo['id']; ?>" 
                           class="btn btn-primary btn-custom">
                            <i class="fas fa-redo me-2"></i>Tekrar Dene
                        </a>
                    <?php endif; ?>
                    
                    <a href="mailto:<EMAIL>?subject=Ödeme Hatası&body=İşlem No: <?php echo $merchant_oid; ?>%0AHata: <?php echo urlencode($friendlyErrorMsg); ?>" 
                       class="btn btn-warning btn-custom">
                        <i class="fas fa-headset me-2"></i>Destek
                    </a>
                    
                    <a href="/" class="btn btn-secondary btn-custom">
                        <i class="fas fa-home me-2"></i>Ana Sayfa
                    </a>
                </div>
                
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Tüm ödemeleriniz SSL ile güvence altındadır
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
