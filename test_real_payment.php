<?php
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
require_once 'includes/config.php';
require_once 'includes/functions.php';

 

// Test için müşteri ve abonelik bilgileri
$customerId = 6; // URL'den gelen customer_id
$subscriptionId = 1; // URL'den gelen subscription_id

$customer = getCustomer($customerId);
$subscription = getSubscription($subscriptionId);

if (!$customer || !$subscription) {
    die('Müşteri veya abonelik bulunamadı!');
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerçek Ödeme Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Gerçek Ödeme Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Bilgileri</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Müşteri:</strong> <?php echo htmlspecialchars($customer['name']); ?></p>
                        <p><strong>E-posta:</strong> <?php echo htmlspecialchars($customer['email']); ?></p>
                        <p><strong>Abonelik:</strong> <?php echo htmlspecialchars($subscription['name']); ?></p>
                        <p><strong>Tutar:</strong> <?php echo number_format($subscription['price'], 2); ?> ₺</p>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <h6>Test Adımları:</h6>
                            <ol>
                                <li>Debug loglarını temizle</li>
                                <li>Gerçek ödeme sayfasına git</li>
                                <li>Test kartı ile ödeme yap</li>
                                <li>Callback loglarını kontrol et</li>
                                <li>Veritabanında kart kaydını kontrol et</li>
                            </ol>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="clearLogs()">
                                1. Debug Loglarını Temizle
                            </button>
                            <a href="index.php?customer_id=<?php echo $customerId; ?>&subscription_id=<?php echo $subscriptionId; ?>" 
                               class="btn btn-primary" target="_blank">
                                2. Gerçek Ödeme Sayfası
                            </a>
                            <button class="btn btn-info" onclick="checkLogs()">
                                3. Callback Loglarını Kontrol Et
                            </button>
                            <button class="btn btn-success" onclick="checkCards()">
                                4. Kart Kayıtlarını Kontrol Et
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>PayTR Test Kart Bilgileri</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Kart Numarası:</strong> ****************</p>
                        <p><strong>Son Kullanma:</strong> 12/30</p>
                        <p><strong>CVV:</strong> 123</p>
                        <p><strong>Kart Sahibi:</strong> Test User</p>
                        
                        <div class="alert alert-warning">
                            <small>Bu bilgileri PayTR test ortamında kullanın.</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Sonuçları</h5>
                    </div>
                    <div class="card-body">
                        <div id="results" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                            Test sonuçları burada görünecek...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="test_callback.php" class="btn btn-secondary">← Callback Test</a>
            <a href="debug_logs.php" class="btn btn-info">Debug Logs</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `[${timestamp}] ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function clearLogs() {
            clearResults();
            log('Debug logları temizleniyor...');
            
            fetch('debug_logs.php?clear_debug=1')
                .then(response => response.text())
                .then(data => {
                    log('✓ Debug logları temizlendi');
                    log('✓ Artık gerçek ödeme yapabilirsiniz');
                    log('');
                    log('Adımlar:');
                    log('1. "Gerçek Ödeme Sayfası" linkine tıklayın');
                    log('2. PayTR test kart bilgilerini kullanın');
                    log('3. Ödemeyi tamamlayın');
                    log('4. Bu sayfaya geri dönün ve logları kontrol edin');
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
        
        function checkLogs() {
            clearResults();
            log('Callback logları kontrol ediliyor...');
            
            fetch('test_real_payment_api.php?action=check_logs')
                .then(response => response.text())
                .then(data => {
                    log('=== CALLBACK LOGLARI ===');
                    log(data);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
        
        function checkCards() {
            clearResults();
            log('Kart kayıtları kontrol ediliyor...');
            
            fetch('test_real_payment_api.php?action=check_cards&customer_id=<?php echo $customerId; ?>')
                .then(response => response.text())
                .then(data => {
                    log('=== KART KAYITLARI ===');
                    log(data);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
        
        // Sayfa yüklendiğinde bilgi ver
        window.addEventListener('load', function() {
            log('Gerçek ödeme test sistemi hazır');
            log('Müşteri ID: <?php echo $customerId; ?>');
            log('Abonelik ID: <?php echo $subscriptionId; ?>');
            log('');
            log('İlk olarak "Debug Loglarını Temizle" butonuna basın');
        });
    </script>
</body>
</html>
