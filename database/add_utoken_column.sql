-- PayTR utoken sütunu ekleme migration
-- Bu script mevcut payment_cards tablosuna utoken sütunu ekler

-- <PERSON><PERSON> sütunun var olup olmadığını kontrol et
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'payment_cards' 
AND column_name = 'utoken';

-- <PERSON><PERSON><PERSON> sütun yoksa ekle
SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE payment_cards ADD COLUMN utoken VARCHAR(255) NULL AFTER card_type, ADD INDEX idx_utoken (utoken)',
    'SELECT "utoken column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- <PERSON><PERSON><PERSON> göster
SELECT 
    CASE 
        WHEN @col_exists = 0 THEN 'utoken sütunu başarıyla eklendi'
        ELSE 'utoken sütunu zaten mevcut'
    END as result;
