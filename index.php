<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/paytr.php';

// URL'den müşteri ID'si ve abonelik ID'si al
$customerId = $_GET['customer_id'] ?? null;
$subscriptionId = $_GET['subscription_id'] ?? null;

if (!$customerId || !$subscriptionId) {
    die('Geçersiz link!');
}

// Müşteri ve abonelik bilgilerini getir
$customer = getCustomer($customerId);
$subscriptions = getAllSubscriptions();
$subscription = null;

foreach ($subscriptions as $sub) {
    if ($sub['id'] == $subscriptionId) {
        $subscription = $sub;
        break;
    }
}

if (!$customer || !$subscription) {
    die('Müşteri veya abonelik bulunamadı!');
}

// Ödeme formu oluşturuldu mu kontrol et
$paymentFormCreated = false;
$paymentFormData = null;

if ($_POST && isset($_POST['create_payment'])) {
    try {
        // PayTR ödeme formu oluştur
        $paymentFormData = PayTR::createPaymentForm($customerId, $subscriptionId, $subscription['price'], $customer);

        if (isset($paymentFormData['status']) && $paymentFormData['status'] === 'success') {
            $paymentFormCreated = true;
        } else {
            $error = $paymentFormData['error'] ?? 'PayTR token alınamadı.';
        }
    } catch (Exception $e) {
        error_log('Ödeme formu oluşturma hatası: ' . $e->getMessage());
        $error = 'Ödeme formu oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Abonelik Ödemesi - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .payment-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .payment-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .payment-body {
            padding: 2rem;
        }
        .subscription-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .price-tag {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .btn-payment {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s;
        }
        .btn-payment:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .customer-info {
            border-left: 4px solid #667eea;
            padding-left: 1rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="payment-card">
                    <div class="payment-header">
                        <i class="fas fa-credit-card fa-3x mb-3"></i>
                        <h2>Abonelik Ödemesi</h2>
                        <p class="mb-0">Güvenli ödeme sistemi ile aboneliğinizi başlatın</p>
                    </div>
                    
                    <div class="payment-body">
                        <!-- Müşteri Bilgileri -->
                        <div class="customer-info">
                            <h5><i class="fas fa-user"></i> Müşteri Bilgileri</h5>
                            <p class="mb-1"><strong>Ad Soyad:</strong> <?php echo htmlspecialchars($customer['name']); ?></p>
                            <p class="mb-1"><strong>E-posta:</strong> <?php echo htmlspecialchars($customer['email']); ?></p>
                            <p class="mb-0"><strong>Telefon:</strong> <?php echo htmlspecialchars($customer['phone']); ?></p>
                        </div>
                        
                        <!-- Abonelik Bilgileri -->
                        <div class="subscription-info">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h4><i class="fas fa-star text-warning"></i> <?php echo htmlspecialchars($subscription['name']); ?></h4>
                                    <p class="text-muted mb-0"><?php echo htmlspecialchars($subscription['description']); ?></p>
                                    <small class="text-muted">Aylık otomatik yenileme</small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="price-tag"><?php echo number_format($subscription['price'], 2); ?> ₺</div>
                                    <small class="text-muted">/ ay</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Ödeme Bilgileri -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Önemli Bilgiler:</strong>
                            <ul class="mb-0 mt-2">
                                <li>İlk ödemenizi yaptıktan sonra aboneliğiniz aktif olacaktır</li>
                                <li>Kart bilgileriniz güvenli şekilde kaydedilecektir</li>
                                <li>Her ay otomatik olarak ödeme alınacaktır</li>
                                <li>İstediğiniz zaman aboneliğinizi iptal edebilirsiniz</li>
                            </ul>
                        </div>

                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (!$paymentFormCreated): ?>
                            <!-- Ödeme Başlat Butonu -->
                            <form method="POST" class="text-center">
                                <button type="submit" name="create_payment" class="btn btn-primary btn-payment">
                                    <i class="fas fa-lock"></i> Güvenli Ödeme Yap
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt"></i> 256-bit SSL ile korunmaktadır
                                    </small>
                                </div>
                            </form>
                        <?php else: ?>
                            <!-- PayTR Ödeme Formu -->
                            <div class="text-center">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    Ödeme formu hazırlandı. PayTR'ye yönlendiriliyorsunuz...
                                </div>
                                
                                <form method="POST" action="https://www.paytr.com/odeme/guvenli/<?php echo $paymentFormData['token']; ?>" id="paytr_form">
                                    <?php foreach ($paymentFormData['form_data'] as $key => $value): ?>
                                        <input type="hidden" name="<?php echo $key; ?>" value="<?php echo htmlspecialchars($value); ?>">
                                    <?php endforeach; ?>
                                    <button type="submit" class="btn btn-success btn-payment">
                                        <i class="fas fa-arrow-right"></i> PayTR'ye Git
                                    </button>
                                </form>
                                
                                <script>
                                    // 3 saniye sonra otomatik yönlendir
                                    setTimeout(function() {
                                        document.getElementById('paytr_form').submit();
                                    }, 3000);
                                </script>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Güvenlik Bilgileri -->
                        <div class="row mt-4 text-center">
                            <div class="col-4">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <small class="d-block text-muted">SSL Güvenlik</small>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-lock fa-2x text-primary mb-2"></i>
                                <small class="d-block text-muted">Şifreli Ödeme</small>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-credit-card fa-2x text-warning mb-2"></i>
                                <small class="d-block text-muted">Güvenli Kart</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Destek Bilgileri -->
                <div class="text-center mt-4">
                    <div class="card bg-transparent border-light text-white">
                        <div class="card-body">
                            <h6><i class="fas fa-headset"></i> Destek</h6>
                            <p class="mb-0">
                                Herhangi bir sorunuz için: 
                                <a href="mailto:<EMAIL>" class="text-white"><EMAIL></a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
