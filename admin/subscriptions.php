<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// <PERSON><PERSON>ş kontrolü
if (!isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Çıkış işlemi
if (isset($_GET['logout'])) {
    adminLogout();
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';

// Abonelik planı ekleme
if (isset($_POST['add_subscription'])) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("INSERT INTO subscriptions (name, price, description, status) VALUES (?, ?, ?, 'active')");
        $result = $stmt->execute([
            $_POST['name'],
            floatval($_POST['price']),
            $_POST['description']
        ]);

        if ($result) {
            $message = 'Abonelik planı başarıyla eklendi!';
            $messageType = 'success';
        } else {
            $message = 'Abonelik planı eklenirken hata oluştu!';
            $messageType = 'danger';
        }
    } catch (PDOException $e) {
        error_log("Abonelik ekleme hatası: " . $e->getMessage());
        $message = 'Abonelik planı eklenirken hata oluştu!';
        $messageType = 'danger';
    }
}

// Abonelik planı güncelleme
if (isset($_POST['update_subscription'])) {
    global $pdo;

    try {
        $id = $_POST['subscription_id'];
        $stmt = $pdo->prepare("UPDATE subscriptions SET name = ?, price = ?, description = ?, status = ? WHERE id = ?");
        $result = $stmt->execute([
            $_POST['name'],
            floatval($_POST['price']),
            $_POST['description'],
            $_POST['status'],
            $id
        ]);

        if ($result) {
            $message = 'Abonelik planı başarıyla güncellendi!';
            $messageType = 'success';
        } else {
            $message = 'Abonelik planı güncellenirken hata oluştu!';
            $messageType = 'danger';
        }
    } catch (PDOException $e) {
        error_log("Abonelik güncelleme hatası: " . $e->getMessage());
        $message = 'Abonelik planı güncellenirken hata oluştu!';
        $messageType = 'danger';
    }
}

// Müşteri aboneliği iptal etme
if (isset($_GET['cancel_customer_subscription']) && is_numeric($_GET['cancel_customer_subscription'])) {
    $id = $_GET['cancel_customer_subscription'];
    $reason = $_GET['reason'] ?? 'admin_action';

    if (cancelSubscription($id, $reason)) {
        $message = 'Müşteri aboneliği iptal edildi!';
        $messageType = 'success';
    } else {
        $message = 'Abonelik iptal edilirken hata oluştu!';
        $messageType = 'danger';
    }
}

// Abonelik duraklat
if (isset($_GET['pause_subscription']) && is_numeric($_GET['pause_subscription'])) {
    $id = $_GET['pause_subscription'];
    $pauseUntil = $_GET['pause_until'] ?? date('Y-m-d', strtotime('+1 month'));

    if (pauseSubscription($id, $pauseUntil)) {
        $message = 'Abonelik duraklatıldı!';
        $messageType = 'success';
    } else {
        $message = 'Abonelik duraklatılırken hata oluştu!';
        $messageType = 'danger';
    }
}

// Abonelik devam ettir
if (isset($_GET['resume_subscription']) && is_numeric($_GET['resume_subscription'])) {
    $id = $_GET['resume_subscription'];

    if (resumeSubscription($id)) {
        $message = 'Abonelik devam ettirildi!';
        $messageType = 'success';
    } else {
        $message = 'Abonelik devam ettirilirken hata oluştu!';
        $messageType = 'danger';
    }
}

// Verileri getir
$subscriptions = getAllSubscriptions();
$customerSubscriptions = getAllCustomerSubscriptions();
$customers = getAllCustomers();

// Müşteri isimlerini eşleştir
$customerNames = [];
foreach ($customers as $customer) {
    $customerNames[$customer['id']] = $customer;
}

$subscriptionNames = [];
foreach ($subscriptions as $subscription) {
    $subscriptionNames[$subscription['id']] = $subscription;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Abonelikler - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 10px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }
        .table-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .subscription-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .subscription-card:hover {
            transform: translateY(-5px);
        }
        .price-tag {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4">
                        <h4><i class="fas fa-crown"></i> Admin Panel</h4>
                        <small>PayTR Abonelik Sistemi</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a class="nav-link" href="customers.php">
                            <i class="fas fa-users"></i> Müşteriler
                        </a>
                        <a class="nav-link active" href="subscriptions.php">
                            <i class="fas fa-credit-card"></i> Abonelikler
                        </a>
                        <a class="nav-link" href="payments.php">
                            <i class="fas fa-money-bill-wave"></i> Ödemeler
                        </a>
                        <a class="nav-link" href="?logout=1">
                            <i class="fas fa-sign-out-alt"></i> Çıkış
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-credit-card"></i> Abonelik Yönetimi</h2>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubscriptionModal">
                            <i class="fas fa-plus"></i> Yeni Plan
                        </button>
                    </div>
                    
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Abonelik Planları -->
                    <div class="row mb-5">
                        <div class="col-12">
                            <h4><i class="fas fa-list"></i> Abonelik Planları</h4>
                        </div>
                        <?php foreach ($subscriptions as $subscription): ?>
                            <div class="col-md-4 mb-3">
                                <div class="card subscription-card">
                                    <div class="card-body text-center">
                                        <h5 class="card-title"><?php echo htmlspecialchars($subscription['name']); ?></h5>
                                        <div class="price-tag mb-2"><?php echo number_format($subscription['price'], 2); ?> ₺</div>
                                        <small class="text-muted">/ ay</small>
                                        <p class="card-text mt-3"><?php echo htmlspecialchars($subscription['description']); ?></p>
                                        <span class="badge bg-<?php echo $subscription['status'] === 'active' ? 'success' : 'danger'; ?> mb-3">
                                            <?php echo $subscription['status'] === 'active' ? 'Aktif' : 'Pasif'; ?>
                                        </span>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editSubscription(<?php echo htmlspecialchars(json_encode($subscription)); ?>)">
                                                <i class="fas fa-edit"></i> Düzenle
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Müşteri Abonelikleri -->
                    <div class="card table-card">
                        <div class="card-header">
                            <h5><i class="fas fa-users"></i> Müşteri Abonelikleri</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($customerSubscriptions)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Henüz aktif abonelik yok</h5>
                                    <p class="text-muted">Müşterilere abonelik ekledikçe burada görünecektir.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>Müşteri</th>
                                                <th>Plan</th>
                                                <th>Durum</th>
                                                <th>Sonraki Ödeme</th>
                                                <th>Başlangıç</th>
                                                <th>İşlemler</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($customerSubscriptions as $customerSub): ?>
                                                <tr>
                                                    <td><?php echo $customerSub['id']; ?></td>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($customerNames[$customerSub['customer_id']]['name'] ?? 'Bilinmeyen'); ?></strong><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($customerNames[$customerSub['customer_id']]['email'] ?? ''); ?></small>
                                                    </td>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($subscriptionNames[$customerSub['subscription_id']]['name'] ?? 'Bilinmeyen'); ?></strong><br>
                                                        <small class="text-muted"><?php echo number_format($subscriptionNames[$customerSub['subscription_id']]['price'] ?? 0, 2); ?> ₺/ay</small>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $statusClass = '';
                                                        $statusText = '';
                                                        switch ($customerSub['status']) {
                                                            case 'active':
                                                                $statusClass = 'bg-success';
                                                                $statusText = 'Aktif';
                                                                break;
                                                            case 'pending':
                                                                $statusClass = 'bg-warning';
                                                                $statusText = 'Beklemede';
                                                                break;
                                                            case 'cancelled':
                                                                $statusClass = 'bg-danger';
                                                                $statusText = 'İptal';
                                                                break;
                                                            case 'suspended':
                                                                $statusClass = 'bg-secondary';
                                                                $statusText = 'Askıda';
                                                                break;
                                                            default:
                                                                $statusClass = 'bg-secondary';
                                                                $statusText = ucfirst($customerSub['status']);
                                                        }
                                                        ?>
                                                        <span class="badge <?php echo $statusClass; ?>">
                                                            <?php echo $statusText; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($customerSub['status'] === 'active'): ?>
                                                            <?php echo date('d.m.Y', strtotime($customerSub['next_payment_date'])); ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo date('d.m.Y', strtotime($customerSub['created_at'])); ?></td>
                                                    <td>
                                                        <?php if ($customerSub['status'] === 'active'): ?>
                                                            <div class="btn-group" role="group">
                                                                <a href="?pause_subscription=<?php echo $customerSub['id']; ?>"
                                                                   class="btn btn-sm btn-outline-warning"
                                                                   onclick="return confirm('Bu aboneliği duraklatmak istediğinizden emin misiniz?')"
                                                                   title="Duraklat">
                                                                    <i class="fas fa-pause"></i>
                                                                </a>
                                                                <a href="?cancel_customer_subscription=<?php echo $customerSub['id']; ?>"
                                                                   class="btn btn-sm btn-outline-danger"
                                                                   onclick="return confirm('Bu aboneliği iptal etmek istediğinizden emin misiniz?')"
                                                                   title="İptal">
                                                                    <i class="fas fa-times"></i>
                                                                </a>
                                                            </div>
                                                        <?php elseif ($customerSub['status'] === 'paused'): ?>
                                                            <div class="btn-group" role="group">
                                                                <a href="?resume_subscription=<?php echo $customerSub['id']; ?>"
                                                                   class="btn btn-sm btn-outline-success"
                                                                   onclick="return confirm('Bu aboneliği devam ettirmek istediğinizden emin misiniz?')"
                                                                   title="Devam Ettir">
                                                                    <i class="fas fa-play"></i>
                                                                </a>
                                                                <a href="?cancel_customer_subscription=<?php echo $customerSub['id']; ?>"
                                                                   class="btn btn-sm btn-outline-danger"
                                                                   onclick="return confirm('Bu aboneliği iptal etmek istediğinizden emin misiniz?')"
                                                                   title="İptal">
                                                                    <i class="fas fa-times"></i>
                                                                </a>
                                                            </div>
                                                        <?php endif; ?>
                                                        
                                                        <button class="btn btn-sm btn-outline-info" onclick="generatePaymentLink(<?php echo $customerSub['customer_id']; ?>, <?php echo $customerSub['subscription_id']; ?>)">
                                                            <i class="fas fa-link"></i> Link
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Abonelik Planı Ekleme Modal -->
    <div class="modal fade" id="addSubscriptionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus"></i> Yeni Abonelik Planı</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">Plan Adı</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="price" class="form-label">Aylık Fiyat (₺)</label>
                            <input type="number" step="0.01" class="form-control" id="price" name="price" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Açıklama</label>
                            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                        <button type="submit" name="add_subscription" class="btn btn-primary">Ekle</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Abonelik Planı Düzenleme Modal -->
    <div class="modal fade" id="editSubscriptionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit"></i> Abonelik Planı Düzenle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" id="edit_subscription_id" name="subscription_id">
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Plan Adı</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_price" class="form-label">Aylık Fiyat (₺)</label>
                            <input type="number" step="0.01" class="form-control" id="edit_price" name="price" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Açıklama</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="edit_status" class="form-label">Durum</label>
                            <select class="form-control" id="edit_status" name="status" required>
                                <option value="active">Aktif</option>
                                <option value="inactive">Pasif</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                        <button type="submit" name="update_subscription" class="btn btn-primary">Güncelle</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Ödeme Linki Modal -->
    <div class="modal fade" id="paymentLinkModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-link"></i> Ödeme Linki</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Ödeme Linki</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="payment_link" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyPaymentLink()">
                                <i class="fas fa-copy"></i> Kopyala
                            </button>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Bu linki müşteriye göndererek ödeme yapmasını sağlayabilirsiniz.
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editSubscription(subscription) {
            document.getElementById('edit_subscription_id').value = subscription.id;
            document.getElementById('edit_name').value = subscription.name;
            document.getElementById('edit_price').value = subscription.price;
            document.getElementById('edit_description').value = subscription.description;
            document.getElementById('edit_status').value = subscription.status;
            
            new bootstrap.Modal(document.getElementById('editSubscriptionModal')).show();
        }
        
        function generatePaymentLink(customerId, subscriptionId) {
            const link = window.location.origin + '/int/index.php?customer_id=' + customerId + '&subscription_id=' + subscriptionId;
            document.getElementById('payment_link').value = link;
            
            new bootstrap.Modal(document.getElementById('paymentLinkModal')).show();
        }
        
        function copyPaymentLink() {
            const linkInput = document.getElementById('payment_link');
            linkInput.select();
            linkInput.setSelectionRange(0, 99999);
            navigator.clipboard.writeText(linkInput.value);
            
            // Toast bildirimi göster
            alert('Link kopyalandı!');
        }
    </script>
</body>
</html>
