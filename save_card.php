<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/paytr.php';

// URL'den müşteri ID'si al
$customerId = $_GET['customer_id'] ?? null;

if (!$customerId || !is_numeric($customerId) || $customerId <= 0) {
    http_response_code(400);
    die('Geçersiz müşteri ID!');
}

// Müşteri bilgilerini getir
$customer = getCustomer($customerId);

if (!$customer) {
    http_response_code(404);
    die('Müşteri bulunamadı!');
}

// Müşteri aktif mi kontrol et
if ($customer['status'] !== 'active') {
    http_response_code(403);
    die('Bu müşteri hesabı aktif değil!');
}

// Müşterinin zaten kayıtlı kartı var mı kontrol et
$existingCard = getCustomerCard($customerId);

// Kart kaydetme formu oluşturuldu mu kontrol et
$cardSaveFormCreated = false;
$cardSaveFormData = null;

if ($_POST && isset($_POST['save_card'])) {
    // CSRF token kontrolü (basit rate limiting)
    if (!isset($_SESSION['last_card_save_request']) ||
        (time() - $_SESSION['last_card_save_request']) > 10) {

        $_SESSION['last_card_save_request'] = time();

        try {
            // PayTR kart kaydetme formu oluştur
            $cardSaveFormData = PayTR::createCardSaveForm($customerId, $customer);

            if (isset($cardSaveFormData['status']) && $cardSaveFormData['status'] === 'success') {
                $cardSaveFormCreated = true;
            } else {
                $error = $cardSaveFormData['error'] ?? 'PayTR token alınamadı.';
            }
        } catch (Exception $e) {
            error_log('Kart kaydetme formu oluşturma hatası: ' . $e->getMessage());
            $error = 'Kart kaydetme formu oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        }
    } else {
        $error = 'Çok hızlı istek gönderiyorsunuz. Lütfen 10 saniye bekleyin.';
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kart Kaydetme - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .card-save-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .card-save-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .card-save-body {
            padding: 2rem;
        }
        .customer-info {
            border-left: 4px solid #28a745;
            padding-left: 1rem;
            margin-bottom: 2rem;
        }
        .btn-save-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s;
        }
        .btn-save-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .existing-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 2px solid #28a745;
        }
        .card-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }
        .security-features {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card-save-container">
                    <div class="card-save-header">
                        <i class="fas fa-credit-card fa-3x mb-3"></i>
                        <h2>Kart Bilgilerini Kaydet</h2>
                        <p class="mb-0">Gelecekteki ödemeleriniz için kartınızı güvenle kaydedin</p>
                    </div>
                    
                    <div class="card-save-body">
                        <!-- Müşteri Bilgileri -->
                        <div class="customer-info">
                            <h5><i class="fas fa-user"></i> Müşteri Bilgileri</h5>
                            <p class="mb-1"><strong>Ad Soyad:</strong> <?php echo htmlspecialchars($customer['name']); ?></p>
                            <p class="mb-1"><strong>E-posta:</strong> <?php echo htmlspecialchars($customer['email']); ?></p>
                            <p class="mb-0"><strong>Telefon:</strong> <?php echo htmlspecialchars($customer['phone']); ?></p>
                        </div>
                        
                        <?php if ($existingCard): ?>
                            <!-- Mevcut Kart Bilgisi -->
                            <div class="existing-card">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle card-icon text-success"></i>
                                    <div>
                                        <h5 class="mb-1">Kayıtlı Kartınız Mevcut</h5>
                                        <p class="mb-1">
                                            <strong>Kart:</strong> <?php echo htmlspecialchars($existingCard['card_mask']); ?>
                                            <?php if ($existingCard['card_type']): ?>
                                                <span class="badge bg-primary ms-2"><?php echo htmlspecialchars($existingCard['card_type']); ?></span>
                                            <?php endif; ?>
                                        </p>
                                        <small class="text-muted">
                                            Kayıt Tarihi: <?php echo date('d.m.Y H:i', strtotime($existingCard['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Bilgi:</strong> Zaten kayıtlı bir kartınız bulunmaktadır. 
                                        Yeni bir kart kaydetmek isterseniz, mevcut kart bilgileriniz güncellenecektir.
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Kart Kaydetme Açıklaması -->
                        <div class="alert alert-success">
                            <i class="fas fa-shield-alt"></i>
                            <strong>Güvenli Kart Kaydetme:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Kart bilgileriniz PayTR'nin güvenli sunucularında saklanır</li>
                                <li>Aylık ödemeleriniz otomatik olarak çekilir</li>
                                <li>İstediğiniz zaman kartınızı değiştirebilirsiniz</li>
                                <li>Kart bilgileriniz şifrelenerek korunur</li>
                            </ul>
                        </div>
                        
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (!$cardSaveFormCreated): ?>
                            <!-- Kart Kaydetme Başlat Butonu -->
                            <form method="POST" class="text-center">
                                <button type="submit" name="save_card" class="btn btn-success btn-save-card">
                                    <i class="fas fa-save"></i> 
                                    <?php echo $existingCard ? 'Kartı Güncelle' : 'Kart Kaydet'; ?>
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-lock"></i> 256-bit SSL ile korunmaktadır
                                    </small>
                                </div>
                            </form>
                        <?php else: ?>
                            <!-- PayTR Kart Kaydetme Formu -->
                            <div class="text-center">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    Kart kaydetme formu hazırlandı. PayTR'ye yönlendiriliyorsunuz...
                                </div>
                                
                                <form method="POST" action="https://www.paytr.com/odeme/guvenli/<?php echo $cardSaveFormData['token']; ?>" id="paytr_card_form">
                                    <?php foreach ($cardSaveFormData['form_data'] as $key => $value): ?>
                                        <input type="hidden" name="<?php echo $key; ?>" value="<?php echo htmlspecialchars($value); ?>">
                                    <?php endforeach; ?>
                                    <button type="submit" class="btn btn-success btn-save-card">
                                        <i class="fas fa-arrow-right"></i> PayTR'ye Git
                                    </button>
                                </form>
                                
                                <script>
                                    // 3 saniye sonra otomatik yönlendir
                                    setTimeout(function() {
                                        document.getElementById('paytr_card_form').submit();
                                    }, 3000);
                                </script>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Güvenlik Özellikleri -->
                        <div class="security-features">
                            <h6><i class="fas fa-shield-alt"></i> Güvenlik Özellikleri</h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <i class="fas fa-lock fa-2x text-success mb-2"></i>
                                    <small class="d-block">SSL Şifreleme</small>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-server fa-2x text-primary mb-2"></i>
                                    <small class="d-block">Güvenli Sunucu</small>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-user-shield fa-2x text-warning mb-2"></i>
                                    <small class="d-block">Gizlilik Koruması</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Yardım Bilgileri -->
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <i class="fas fa-question-circle"></i>
                                Kart kaydetme işlemi tamamen ücretsizdir. 
                                Sadece aylık abonelik ödemeleriniz için kullanılacaktır.
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Destek Bilgileri -->
                <div class="text-center mt-4">
                    <div class="card bg-transparent border-light text-white">
                        <div class="card-body">
                            <h6><i class="fas fa-headset"></i> Destek</h6>
                            <p class="mb-0">
                                Herhangi bir sorunuz için: 
                                <a href="mailto:<EMAIL>" class="text-white"><EMAIL></a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
