<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

header('Content-Type: text/plain; charset=utf-8');

$action = $_GET['action'] ?? '';

if ($action === 'check_logs') {
    // Debug loglarından PayTR callback bilgilerini çıkar
    $logFile = __DIR__ . '/logs/paytr_debug.log';
    $errorLogFile = ini_get('error_log');
    
    echo "Debug Log Kontrolü\n";
    echo "==================\n";
    
    // PayTR debug logları
    if (file_exists($logFile)) {
        $content = file_get_contents($logFile);
        $lines = explode("\n", $content);
        $paytrLines = array_filter($lines, function($line) {
            return strpos($line, 'PayTR') !== false || strpos($line, 'Callback') !== false;
        });
        
        if (!empty($paytrLines)) {
            echo "PayTR Debug Logları:\n";
            foreach (array_slice($paytrLines, -10) as $line) {
                echo $line . "\n";
            }
        } else {
            echo "PayTR debug logları bulunamadı.\n";
        }
    } else {
        echo "Debug log dosyası bulunamadı.\n";
    }
    
    echo "\n";
    
    // Error logları
    if ($errorLogFile && file_exists($errorLogFile)) {
        $content = file_get_contents($errorLogFile);
        $lines = explode("\n", $content);
        $callbackLines = array_filter($lines, function($line) {
            return strpos($line, 'PayTR Success Callback') !== false || 
                   strpos($line, 'Kart kaydedildi') !== false ||
                   strpos($line, 'Kart token') !== false;
        });
        
        if (!empty($callbackLines)) {
            echo "Error Log'dan Callback Bilgileri:\n";
            foreach (array_slice($callbackLines, -10) as $line) {
                echo $line . "\n";
            }
        } else {
            echo "Error log'da callback bilgisi bulunamadı.\n";
        }
    } else {
        echo "Error log dosyası bulunamadı.\n";
    }
    
} elseif ($action === 'check_cards') {
    $customerId = $_GET['customer_id'] ?? null;
    
    echo "Kart Kayıtları Kontrolü\n";
    echo "=======================\n";
    echo "Müşteri ID: $customerId\n\n";
    
    if (!$customerId) {
        echo "Müşteri ID gerekli!\n";
        exit;
    }
    
    try {
        global $pdo;
        
        // Müşterinin kartlarını getir
        $stmt = $pdo->prepare("
            SELECT * FROM payment_cards 
            WHERE customer_id = ? 
            ORDER BY created_at DESC
        ");
        $stmt->execute([$customerId]);
        $cards = $stmt->fetchAll();
        
        if (empty($cards)) {
            echo "❌ Bu müşteriye ait kart kaydı bulunamadı!\n\n";
            
            // Son ödemeleri kontrol et
            $stmt = $pdo->prepare("
                SELECT * FROM payments 
                WHERE customer_id = ? 
                ORDER BY created_at DESC 
                LIMIT 5
            ");
            $stmt->execute([$customerId]);
            $payments = $stmt->fetchAll();
            
            if (!empty($payments)) {
                echo "Son Ödemeler:\n";
                foreach ($payments as $payment) {
                    $date = date('d.m.Y H:i', strtotime($payment['created_at']));
                    echo "- $date: " . number_format($payment['amount'], 2) . " ₺ - " . strtoupper($payment['status']) . "\n";
                }
            } else {
                echo "Bu müşteriye ait ödeme kaydı da bulunamadı.\n";
            }
            
        } else {
            echo "✅ " . count($cards) . " adet kart kaydı bulundu:\n\n";
            
            foreach ($cards as $card) {
                $date = date('d.m.Y H:i', strtotime($card['created_at']));
                $status = $card['is_active'] ? 'AKTİF' : 'PASİF';
                
                echo "Kart ID: " . $card['id'] . "\n";
                echo "Token: " . substr($card['card_token'], 0, 20) . "...\n";
                echo "Mask: " . ($card['card_mask'] ?? 'N/A') . "\n";
                echo "Tip: " . ($card['card_type'] ?? 'N/A') . "\n";
                echo "Durum: $status\n";
                echo "Tarih: $date\n";
                echo "---\n";
            }
        }
        
        // PayTR loglarını da kontrol et
        echo "\nPayTR Log Kontrolü:\n";
        $stmt = $pdo->prepare("
            SELECT * FROM paytr_logs 
            WHERE post_data LIKE ? 
            ORDER BY created_at DESC 
            LIMIT 3
        ");
        $stmt->execute(["%customer_id\":\"$customerId\"%"]);
        $logs = $stmt->fetchAll();
        
        if (!empty($logs)) {
            echo "Son PayTR logları:\n";
            foreach ($logs as $log) {
                $date = date('d.m.Y H:i', strtotime($log['created_at']));
                echo "- $date: " . $log['merchant_oid'] . " - " . strtoupper($log['status']) . "\n";
            }
        } else {
            echo "Bu müşteriye ait PayTR log bulunamadı.\n";
        }
        
    } catch (Exception $e) {
        echo "HATA: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "Geçersiz action: $action\n";
}
?>
