<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayTR Callback Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .console {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>PayTR Callback Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Callback Gönder</h5>
                    </div>
                    <div class="card-body">
                        <form id="callbackForm">
                            <div class="mb-3">
                                <label class="form-label">Merchant OID</label>
                                <input type="text" class="form-control" id="merchant_oid" value="SUB1S1T<?php echo time(); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select class="form-control" id="status" required>
                                    <option value="success">success</option>
                                    <option value="failed">failed</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Total Amount (kuruş)</label>
                                <input type="number" class="form-control" id="total_amount" value="2999" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Card Token (opsiyonel)</label>
                                <input type="text" class="form-control" id="card_token" placeholder="TEST_CARD_TOKEN_123">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Card Mask (opsiyonel)</label>
                                <input type="text" class="form-control" id="card_mask" placeholder="**** **** **** 1234">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Card Type (opsiyonel)</label>
                                <input type="text" class="form-control" id="card_type" placeholder="visa">
                            </div>
                            <button type="submit" class="btn btn-primary">Test Callback Gönder</button>
                        </form>
                        
                        <hr>
                        
                        <h6>Hızlı Testler</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success btn-sm" onclick="quickTest('SUB', 'success', true)">
                                Başarılı Abonelik + Kart
                            </button>
                            <button class="btn btn-info btn-sm" onclick="quickTest('CARD', 'success', true)">
                                Başarılı Kart Kaydetme
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="quickTest('AUTO', 'success', false)">
                                Başarılı Otomatik Ödeme
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="quickTest('SUB', 'failed', false)">
                                Başarısız Ödeme
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Callback Yanıtı</h5>
                    </div>
                    <div class="card-body">
                        <div id="console" class="console">
                            Test callback gönder...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Son Kayıtlar</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Son Ödemeler</h6>
                                <div id="recentPayments"></div>
                            </div>
                            <div class="col-md-6">
                                <h6>Son Kartlar</h6>
                                <div id="recentCards"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="test_monthly_payments.php" class="btn btn-secondary">← Test Araçları</a>
            <a href="debug_logs.php" class="btn btn-info">Debug Logs</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            console.innerHTML += `[${timestamp}] ${message}\n`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '';
        }
        
        function generateHash(merchant_oid, status, total_amount) {
            // Bu hash client-side'da hesaplanamaz (merchant_key gerekli)
            // Server-side'da hesaplanacak
            return 'HASH_WILL_BE_CALCULATED_SERVER_SIDE';
        }
        
        function quickTest(type, status, includeCard) {
            const timestamp = Math.floor(Date.now() / 1000);
            let merchant_oid;
            
            switch(type) {
                case 'SUB':
                    merchant_oid = `SUB1S1T${timestamp}`;
                    break;
                case 'CARD':
                    merchant_oid = `CARD1T${timestamp}`;
                    break;
                case 'AUTO':
                    merchant_oid = `AUTO1S1T${timestamp}`;
                    break;
            }
            
            document.getElementById('merchant_oid').value = merchant_oid;
            document.getElementById('status').value = status;
            document.getElementById('total_amount').value = type === 'CARD' ? '100' : '2999';
            
            if (includeCard) {
                document.getElementById('card_token').value = `TEST_CARD_TOKEN_${timestamp}`;
                document.getElementById('card_mask').value = '**** **** **** 1234';
                document.getElementById('card_type').value = 'visa';
            } else {
                document.getElementById('card_token').value = '';
                document.getElementById('card_mask').value = '';
                document.getElementById('card_type').value = '';
            }
            
            sendCallback();
        }
        
        function sendCallback() {
            clearConsole();
            log('Callback gönderiliyor...');
            
            const formData = new FormData();
            formData.append('merchant_oid', document.getElementById('merchant_oid').value);
            formData.append('status', document.getElementById('status').value);
            formData.append('total_amount', document.getElementById('total_amount').value);
            
            const cardToken = document.getElementById('card_token').value;
            if (cardToken) {
                formData.append('card_token', cardToken);
                formData.append('card_mask', document.getElementById('card_mask').value);
                formData.append('card_type', document.getElementById('card_type').value);
            }
            
            // Hash server-side'da hesaplanacak
            formData.append('test_mode', '1');
            
            fetch('test_callback_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                log('=== CALLBACK YANITI ===');
                log(data);
                
                // Kayıtları güncelle
                setTimeout(loadRecentData, 1000);
            })
            .catch(error => {
                log('HATA: ' + error);
            });
        }
        
        function loadRecentData() {
            // Son ödemeler
            fetch('test_callback_api.php?action=recent_payments')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('recentPayments').innerHTML = data;
                });
                
            // Son kartlar
            fetch('test_callback_api.php?action=recent_cards')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('recentCards').innerHTML = data;
                });
        }
        
        document.getElementById('callbackForm').addEventListener('submit', function(e) {
            e.preventDefault();
            sendCallback();
        });
        
        // Sayfa yüklendiğinde son kayıtları yükle
        loadRecentData();
    </script>
</body>
</html>
