<?php
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/paytr.php';

// PayTR'den gelen POST verilerini al
$merchant_oid = $_POST['merchant_oid'] ?? '';
$status = $_POST['status'] ?? '';
$card_token = $_POST['card_token'] ?? '';
$card_mask = $_POST['card_mask'] ?? '';
$card_type = $_POST['card_type'] ?? '';
$hash = $_POST['hash'] ?? '';

// Callback doğrulaması
if (!PayTR::verifyCallback($_POST)) {
    die('PAYTR notification failed: bad hash');
}

// Merchant OID'den müşteri ID'sini çıkar (yeni format: CARD{customer_id}T{timestamp})
if (strpos($merchant_oid, 'CARD') === 0) {
    preg_match('/CARD(\d+)T(\d+)/', $merchant_oid, $matches);
    $customerId = $matches[1] ?? null;
    
    if ($customerId) {
        if ($status == 'success' && !empty($card_token)) {
            // Başarılı kart kaydetme
            $cardData = [
                'customer_id' => $customerId,
                'card_token' => $card_token,
                'card_mask' => $card_mask,
                'card_type' => $card_type
            ];
            
            if (addPaymentCard($cardData)) {
                // Başarı sayfasına yönlendir
                header('Location: card_save_success.php?customer_id=' . $customerId);
                exit;
            } else {
                // Hata sayfasına yönlendir
                header('Location: card_save_fail.php?customer_id=' . $customerId . '&error=save_failed');
                exit;
            }
        } else {
            // Başarısız kart kaydetme
            header('Location: card_save_fail.php?customer_id=' . $customerId . '&error=card_save_failed');
            exit;
        }
    }
}

// PayTR log kaydet
addPayTRLog([
    'merchant_oid' => $merchant_oid,
    'status' => $status,
    'total_amount' => 0,
    'post_data' => $_POST
]);

echo "OK";
?>
