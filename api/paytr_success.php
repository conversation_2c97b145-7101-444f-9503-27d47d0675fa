<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/paytr.php';

// IP whitelist kontrolü (PayTR IP'leri)
$allowedIPs = [
    '*************/24',
    '*************/24',
    '127.0.0.1', // Test için
    '::1' // Test için
];

$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
$ipAllowed = false;

foreach ($allowedIPs as $allowedIP) {
    if (strpos($allowedIP, '/') !== false) {
        // CIDR notation
        list($subnet, $mask) = explode('/', $allowedIP);
        if ((ip2long($clientIP) & ~((1 << (32 - $mask)) - 1)) == ip2long($subnet)) {
            $ipAllowed = true;
            break;
        }
    } else {
        // Exact IP
        if ($clientIP === $allowedIP) {
            $ipAllowed = true;
            break;
        }
    }
}

if (!$ipAllowed && !PAYTR_TEST_MODE) {
    error_log("PayTR callback from unauthorized IP: $clientIP");
    http_response_code(403);
    die('Unauthorized IP');
}

// Rate limiting - aynı merchant_oid için 1 dakikada en fazla 3 callback
$merchant_oid = $_POST['merchant_oid'] ?? '';
if (!empty($merchant_oid)) {
    $cacheKey = 'paytr_callback_' . md5($merchant_oid);
    $attempts = $_SESSION[$cacheKey] ?? [];
    $currentTime = time();

    // 1 dakikadan eski denemeleri temizle
    $attempts = array_filter($attempts, function($attempt) use ($currentTime) {
        return ($currentTime - $attempt) < 60;
    });

    if (count($attempts) >= 3) {
        error_log("PayTR callback rate limit exceeded for: $merchant_oid");
        http_response_code(429);
        die('Rate limit exceeded');
    }

    $attempts[] = $currentTime;
    $_SESSION[$cacheKey] = $attempts;
}

// PayTR'den gelen POST verilerini al ve validate et
$status = $_POST['status'] ?? '';
$total_amount = $_POST['total_amount'] ?? '';
$hash = $_POST['hash'] ?? '';

// Input validation
if (empty($merchant_oid) || empty($status) || empty($hash)) {
    error_log('PayTR callback missing required fields');
    http_response_code(400);
    die('Missing required fields');
}

// Duplicate callback kontrolü
global $pdo;
$stmt = $pdo->prepare("SELECT id FROM paytr_logs WHERE merchant_oid = ? AND status = 'success' LIMIT 1");
$stmt->execute([$merchant_oid]);
if ($stmt->fetch()) {
    error_log("Duplicate PayTR callback for: $merchant_oid");
    echo "OK"; // PayTR'ye OK döndür ama işlem yapma
    exit;
}

// Callback doğrulaması
if (!PayTR::verifyCallback($_POST)) {
    error_log('PayTR callback hash verification failed');
    http_response_code(400);
    die('PAYTR notification failed: bad hash');
}

// Merchant OID'den bilgileri çıkar
if (strpos($merchant_oid, 'SUB') === 0) {
    // Abonelik ödemesi (yeni format: SUB{customer_id}S{subscription_id}T{timestamp})
    preg_match('/SUB(\d+)S(\d+)T(\d+)/', $merchant_oid, $matches);
    $customerId = $matches[1] ?? null;
    $subscriptionId = $matches[2] ?? null;
    
    if ($customerId && $subscriptionId) {
        // Ödeme durumuna göre işlem yap
        if ($status == 'success') {
            // Başarılı ödeme
            $amount = $total_amount / 100; // Kuruştan TL'ye çevir
            
            // Ödeme kaydı ekle
            addPayment([
                'customer_id' => $customerId,
                'subscription_id' => $subscriptionId,
                'amount' => $amount,
                'status' => 'success',
                'paytr_transaction_id' => $merchant_oid
            ]);
            
            // Müşteri aboneliğini aktif yap
            global $pdo;
            $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'active', next_payment_date = DATE_ADD(NOW(), INTERVAL 1 MONTH) WHERE customer_id = ? AND subscription_id = ?");
            $stmt->execute([$customerId, $subscriptionId]);

            // Kart bilgilerini kaydet (PayTR'den gelen kart bilgileri)
            if (isset($_POST['card_token']) && !empty($_POST['card_token'])) {
                // Mevcut kartı kontrol et
                $stmt = $pdo->prepare("SELECT id FROM payment_cards WHERE customer_id = ? AND card_token = ?");
                $stmt->execute([$customerId, $_POST['card_token']]);

                if (!$stmt->fetch()) {
                    // Yeni kart kaydet
                    addPaymentCard([
                        'customer_id' => $customerId,
                        'card_token' => $_POST['card_token'],
                        'card_mask' => $_POST['card_mask'] ?? $_POST['masked_card'] ?? '**** **** **** ****',
                        'card_type' => $_POST['card_type'] ?? $_POST['card_brand'] ?? 'unknown'
                    ]);

                    error_log("Kart kaydedildi - Customer: $customerId, Token: " . substr($_POST['card_token'], 0, 10) . "...");
                }
            }

            // Debug: Gelen tüm POST verilerini logla
            error_log("PayTR Success Callback - POST data: " . print_r($_POST, true));
            
            echo "OK";
        } else {
            // Başarısız ödeme
            addPayment([
                'customer_id' => $customerId,
                'subscription_id' => $subscriptionId,
                'amount' => $total_amount / 100,
                'status' => 'failed',
                'paytr_transaction_id' => $merchant_oid
            ]);
            
            echo "OK";
        }
    }
} elseif (strpos($merchant_oid, 'AUTO') === 0) {
    // Otomatik ödeme (yeni format: AUTO{customer_id}S{subscription_id}T{timestamp})
    preg_match('/AUTO(\d+)S(\d+)T(\d+)/', $merchant_oid, $matches);
    $customerId = $matches[1] ?? null;
    $subscriptionId = $matches[2] ?? null;
    
    if ($customerId && $subscriptionId) {
        if ($status == 'success') {
            // Başarılı otomatik ödeme
            $amount = $total_amount / 100;
            
            addPayment([
                'customer_id' => $customerId,
                'subscription_id' => $subscriptionId,
                'amount' => $amount,
                'status' => 'success',
                'paytr_transaction_id' => $merchant_oid
            ]);
            
            // Sonraki ödeme tarihini güncelle
            $stmt = $pdo->prepare("UPDATE customer_subscriptions SET next_payment_date = DATE_ADD(NOW(), INTERVAL 1 MONTH) WHERE customer_id = ? AND subscription_id = ?");
            $stmt->execute([$customerId, $subscriptionId]);
            
            echo "OK";
        } else {
            // Başarısız otomatik ödeme
            addPayment([
                'customer_id' => $customerId,
                'subscription_id' => $subscriptionId,
                'amount' => $total_amount / 100,
                'status' => 'failed',
                'paytr_transaction_id' => $merchant_oid
            ]);
            
            // Aboneliği askıya al
            $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'suspended' WHERE customer_id = ? AND subscription_id = ?");
            $stmt->execute([$customerId, $subscriptionId]);
            
            echo "OK";
        }
    }
}

// PayTR log kaydet
addPayTRLog([
    'merchant_oid' => $merchant_oid,
    'status' => $status,
    'total_amount' => $total_amount ? $total_amount / 100 : 0,
    'post_data' => $_POST
]);
?>
