<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Başarısız ödeme sayfası
$customerId = $_GET['customer_id'] ?? null;
$subscriptionId = $_GET['subscription_id'] ?? null;
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme Başarısız - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .result-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
        }
        .result-icon {
            font-size: 4rem;
            color: #ff6b6b;
            margin-bottom: 2rem;
        }
        .btn-retry {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 50px;
        }
    </style>
</head>
<body>
    <div class="result-card">
        <i class="fas fa-times-circle result-icon"></i>
        <h2 class="text-danger mb-3">Ödeme Başarısız</h2>
        <p class="text-muted mb-4">
            Üzgünüz, ödemeniz tamamlanamadı. Lütfen kart bilgilerinizi kontrol ederek tekrar deneyin.
        </p>
        
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Olası Nedenler:</strong>
            <ul class="mb-0 mt-2 text-start">
                <li>Kart limitiniz yetersiz olabilir</li>
                <li>Kart bilgileri hatalı girilmiş olabilir</li>
                <li>Kartınız internet alışverişine kapalı olabilir</li>
                <li>Geçici bir teknik sorun yaşanmış olabilir</li>
            </ul>
        </div>
        
        <?php if ($customerId && $subscriptionId): ?>
            <a href="../index.php?customer_id=<?php echo $customerId; ?>&subscription_id=<?php echo $subscriptionId; ?>" 
               class="btn btn-primary btn-retry">
                <i class="fas fa-redo"></i> Tekrar Dene
            </a>
        <?php endif; ?>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-headset"></i> 
                Sorun devam ederse destek ekibimizle iletişime geçin: 
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
