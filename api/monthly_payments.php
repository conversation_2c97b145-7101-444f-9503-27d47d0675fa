<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/paytr.php';

// Bu dosya cron job ile çalıştırılmalıdır
// Örnek cron: 0 9 * * * /usr/bin/php /path/to/monthly_payments.php

// Sadece CLI'dan çalışmasını sağla
if (php_sapi_name() !== 'cli' && !isset($_GET['manual_run'])) {
    die('Bu script sadece komut satırından çalıştırılabilir.');
}

echo "Aylık ödeme işlemi başlatılıyor...\n";
echo "Tarih: " . date('Y-m-d H:i:s') . "\n";
echo "Test Modu: " . (PAYTR_TEST_MODE ? 'AKTIF' : 'KAPALI') . "\n\n";

// Bugün ödemesi yapılması gereken abonelikleri bul
$today = date('Y-m-d');
global $pdo;

// Bugün ödemesi yapılacak aktif abonelikleri getir
$stmt = $pdo->prepare("
    SELECT cs.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
           s.name as subscription_name, s.price as subscription_price
    FROM customer_subscriptions cs
    JOIN customers c ON cs.customer_id = c.id
    JOIN subscriptions s ON cs.subscription_id = s.id
    WHERE cs.status = 'active' AND cs.next_payment_date = ?
");
$stmt->execute([$today]);
$customerSubscriptions = $stmt->fetchAll();

$processedCount = 0;
$successCount = 0;
$failedCount = 0;

foreach ($customerSubscriptions as $customerSub) {
    $processedCount++;

    // Müşteri bilgileri zaten JOIN ile geldi
    $customer = [
        'id' => $customerSub['customer_id'],
        'name' => $customerSub['customer_name'],
        'email' => $customerSub['customer_email'],
        'phone' => $customerSub['customer_phone']
    ];

    $subscription = [
        'id' => $customerSub['subscription_id'],
        'name' => $customerSub['subscription_name'],
        'price' => $customerSub['subscription_price']
    ];

    // Kayıtlı kart bilgilerini bul
    $customerCard = getCustomerCard($customerSub['customer_id']);

    if (!$customerCard) {
        echo "Kayıtlı kart bulunamadı: " . $customer['name'] . " (" . $customer['email'] . ")\n";

        // Aboneliği askıya al
        $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'suspended' WHERE id = ?");
        $stmt->execute([$customerSub['id']]);

        // Başarısız ödeme kaydı ekle
        addPayment([
            'customer_id' => $customerSub['customer_id'],
            'subscription_id' => $customerSub['subscription_id'],
            'amount' => $subscription['price'],
            'status' => 'failed',
            'paytr_transaction_id' => 'NOCARD' . time()
        ]);

        $failedCount++;
        continue;
    }

    echo "Ödeme çekiliyor: " . $customer['name'] . " - " . $subscription['name'] . " - " . $subscription['price'] . " ₺\n";
    
    // PayTR ile ödeme çek
    $result = PayTR::chargeCard(
        $customerCard['card_token'],
        $customerSub['customer_id'],
        $customerSub['subscription_id'],
        $subscription['price']
    );
    
    if ($result && isset($result['status']) && $result['status'] === 'success') {
        // Başarılı ödeme
        echo "✓ Ödeme başarılı: " . $customer['name'] . "\n";
        
        addPayment([
            'customer_id' => $customerSub['customer_id'],
            'subscription_id' => $customerSub['subscription_id'],
            'amount' => $subscription['price'],
            'status' => 'success',
            'paytr_transaction_id' => $result['transaction_id'] ?? 'AUTO' . $customerSub['customer_id'] . 'S' . $customerSub['subscription_id'] . 'T' . time()
        ]);
        
        // Sonraki ödeme tarihini güncelle
        $stmt = $pdo->prepare("UPDATE customer_subscriptions SET next_payment_date = DATE_ADD(NOW(), INTERVAL 1 MONTH) WHERE id = ?");
        $stmt->execute([$customerSub['id']]);
        
        $successCount++;
        
        // E-posta bildirimi gönder (isteğe bağlı)
        sendPaymentSuccessEmail($customer, $subscription);
        
    } else {
        // Başarısız ödeme
        echo "✗ Ödeme başarısız: " . $customer['name'] . " - Hata: " . ($result['error_message'] ?? 'Bilinmeyen hata') . "\n";
        
        addPayment([
            'customer_id' => $customerSub['customer_id'],
            'subscription_id' => $customerSub['subscription_id'],
            'amount' => $subscription['price'],
            'status' => 'failed',
            'paytr_transaction_id' => 'FAILED_' . time()
        ]);
        
        // 3 başarısız ödeme denemesinden sonra aboneliği askıya al
        $stmt = $pdo->prepare("SELECT COUNT(*) as failed_count FROM payments WHERE customer_id = ? AND subscription_id = ? AND status = 'failed' AND created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $stmt->execute([$customerSub['customer_id'], $customerSub['subscription_id']]);
        $failedCount = $stmt->fetch()['failed_count'];

        if ($failedCount >= 3) {
            $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'suspended' WHERE id = ?");
            $stmt->execute([$customerSub['id']]);
            echo "⚠ Abonelik askıya alındı: " . $customer['name'] . " (3 başarısız ödeme)\n";

            // E-posta bildirimi gönder
            sendSubscriptionSuspendedEmail($customer, $subscription);
        } else {
            // Sonraki ödeme tarihini 3 gün sonraya ertele
            $stmt = $pdo->prepare("UPDATE customer_subscriptions SET next_payment_date = DATE_ADD(NOW(), INTERVAL 3 DAY) WHERE id = ?");
            $stmt->execute([$customerSub['id']]);
        }
        $failedCount++;
        
        // E-posta bildirimi gönder
        sendPaymentFailedEmail($customer, $subscription, $result['error_message'] ?? 'Bilinmeyen hata');
    }
    
    // İşlemler arası kısa bekleme
    sleep(1);
}

// Özet rapor
echo "\n=== ÖZET RAPOR ===\n";
echo "Toplam işlenen: $processedCount\n";
echo "Başarılı: $successCount\n";
echo "Başarısız: $failedCount\n";
echo "Tamamlandı: " . date('Y-m-d H:i:s') . "\n";

// Log kaydet
addMonthlyPaymentLog([
    'processed' => $processedCount,
    'success' => $successCount,
    'failed' => $failedCount,
    'details' => 'Monthly payment processing completed'
]);

// E-posta gönderme fonksiyonları (basit örnekler)
function sendPaymentSuccessEmail($customer, $subscription) {
    // Mail gönderme kodu buraya eklenebilir
    // mail($customer['email'], 'Ödemeniz Alındı', 'Abonelik ödemeniz başarıyla alınmıştır.');
}

function sendPaymentFailedEmail($customer, $subscription, $error) {
    // Mail gönderme kodu buraya eklenebilir
    // mail($customer['email'], 'Ödeme Başarısız', 'Abonelik ödemeniz alınamadı: ' . $error);
}

function sendSubscriptionSuspendedEmail($customer, $subscription) {
    // Mail gönderme kodu buraya eklenebilir
    // mail($customer['email'], 'Abonelik Askıya Alındı', 'Aboneliğiniz ödeme alınamadığı için askıya alınmıştır.');
}
?>
