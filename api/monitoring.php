<?php
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Monitoring ve alerting sistemi
class MonitoringSystem {
    
    // Sistem sağlığını kontrol et
    public static function checkSystemHealth() {
        $health = [
            'status' => 'healthy',
            'timestamp' => date('Y-m-d H:i:s'),
            'checks' => []
        ];
        
        // Veritabanı bağlantısı kontrolü
        $health['checks']['database'] = self::checkDatabase();
        
        // PayTR API bağlantısı kontrolü
        $health['checks']['paytr_api'] = self::checkPayTRAPI();
        
        // Disk alanı kontrolü
        $health['checks']['disk_space'] = self::checkDiskSpace();
        
        // Memory kullanımı kontrolü
        $health['checks']['memory'] = self::checkMemoryUsage();
        
        // Başarısız ödeme oranı kontrolü
        $health['checks']['failed_payments'] = self::checkFailedPaymentRate();
        
        // Genel durum belirleme
        foreach ($health['checks'] as $check) {
            if ($check['status'] !== 'ok') {
                $health['status'] = 'warning';
                if ($check['status'] === 'critical') {
                    $health['status'] = 'critical';
                    break;
                }
            }
        }
        
        return $health;
    }
    
    // Veritabanı kontrolü
    private static function checkDatabase() {
        try {
            global $pdo;
            $start = microtime(true);
            $stmt = $pdo->query("SELECT 1");
            $responseTime = (microtime(true) - $start) * 1000;
            
            return [
                'status' => 'ok',
                'response_time_ms' => round($responseTime, 2),
                'message' => 'Database connection successful'
            ];
        } catch (Exception $e) {
            return [
                'status' => 'critical',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    // PayTR API kontrolü
    private static function checkPayTRAPI() {
        try {
            $start = microtime(true);
            
            // Test query (geçersiz merchant_oid ile)
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://www.paytr.com/odeme/api/query");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, "test=1");
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            
            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $responseTime = (microtime(true) - $start) * 1000;
            curl_close($ch);
            
            if ($httpCode === 200 || $httpCode === 400) { // 400 beklenen (geçersiz data)
                return [
                    'status' => 'ok',
                    'response_time_ms' => round($responseTime, 2),
                    'message' => 'PayTR API accessible'
                ];
            } else {
                return [
                    'status' => 'warning',
                    'response_time_ms' => round($responseTime, 2),
                    'message' => "PayTR API returned HTTP $httpCode"
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'critical',
                'message' => 'PayTR API connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    // Disk alanı kontrolü
    private static function checkDiskSpace() {
        $freeBytes = disk_free_space('.');
        $totalBytes = disk_total_space('.');
        $usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;
        
        $status = 'ok';
        if ($usedPercent > 90) {
            $status = 'critical';
        } elseif ($usedPercent > 80) {
            $status = 'warning';
        }
        
        return [
            'status' => $status,
            'used_percent' => round($usedPercent, 2),
            'free_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
            'message' => "Disk usage: " . round($usedPercent, 2) . "%"
        ];
    }
    
    // Memory kullanımı kontrolü
    private static function checkMemoryUsage() {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        // Memory limit'i byte'a çevir
        $memoryLimitBytes = self::convertToBytes($memoryLimit);
        $usagePercent = ($memoryUsage / $memoryLimitBytes) * 100;
        
        $status = 'ok';
        if ($usagePercent > 90) {
            $status = 'critical';
        } elseif ($usagePercent > 80) {
            $status = 'warning';
        }
        
        return [
            'status' => $status,
            'usage_percent' => round($usagePercent, 2),
            'usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'message' => "Memory usage: " . round($usagePercent, 2) . "%"
        ];
    }
    
    // Başarısız ödeme oranı kontrolü
    private static function checkFailedPaymentRate() {
        try {
            global $pdo;
            
            // Son 24 saatteki ödemeler
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
                FROM payments 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");
            $result = $stmt->fetch();
            
            $total = $result['total'];
            $failed = $result['failed'];
            $failureRate = $total > 0 ? ($failed / $total) * 100 : 0;
            
            $status = 'ok';
            if ($failureRate > 20) {
                $status = 'critical';
            } elseif ($failureRate > 10) {
                $status = 'warning';
            }
            
            return [
                'status' => $status,
                'failure_rate_percent' => round($failureRate, 2),
                'failed_count' => $failed,
                'total_count' => $total,
                'message' => "Payment failure rate: " . round($failureRate, 2) . "% (last 24h)"
            ];
        } catch (Exception $e) {
            return [
                'status' => 'warning',
                'message' => 'Could not check payment failure rate: ' . $e->getMessage()
            ];
        }
    }
    
    // Memory limit string'ini byte'a çevir
    private static function convertToBytes($val) {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int)$val;
        
        switch($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }
        
        return $val;
    }
    
    // Alert gönder
    public static function sendAlert($type, $message, $severity = 'warning') {
        $alertData = [
            'type' => $type,
            'message' => $message,
            'severity' => $severity,
            'timestamp' => date('Y-m-d H:i:s'),
            'server' => $_SERVER['SERVER_NAME'] ?? 'unknown'
        ];
        
        // Log'a kaydet
        error_log("ALERT [$severity]: $type - $message");
        
        // E-posta gönder (isteğe bağlı)
        if ($severity === 'critical') {
            self::sendEmailAlert($alertData);
        }
        
        // Slack/Discord webhook (isteğe bağlı)
        // self::sendWebhookAlert($alertData);
        
        return true;
    }
    
    // E-posta alert
    private static function sendEmailAlert($alertData) {
        $to = '<EMAIL>'; // Konfigürasyondan alınabilir
        $subject = '[CRITICAL] PayTR Abonelik Sistemi Alert';
        $message = "Alert Type: {$alertData['type']}\n";
        $message .= "Severity: {$alertData['severity']}\n";
        $message .= "Message: {$alertData['message']}\n";
        $message .= "Time: {$alertData['timestamp']}\n";
        $message .= "Server: {$alertData['server']}\n";
        
        $headers = 'From: noreply@' . ($_SERVER['SERVER_NAME'] ?? 'localhost');
        
        // mail($to, $subject, $message, $headers);
    }
}

// API endpoint
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    header('Content-Type: application/json');
    
    $action = $_GET['action'] ?? 'health';
    
    switch ($action) {
        case 'health':
            $health = MonitoringSystem::checkSystemHealth();
            echo json_encode($health, JSON_PRETTY_PRINT);
            break;
            
        case 'alert':
            $type = $_GET['type'] ?? 'test';
            $message = $_GET['message'] ?? 'Test alert';
            $severity = $_GET['severity'] ?? 'warning';
            
            MonitoringSystem::sendAlert($type, $message, $severity);
            echo json_encode(['status' => 'Alert sent']);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}
?>
