<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$customerId = $_GET['customer_id'] ?? null;
$error = $_GET['error'] ?? 'unknown';
$customer = null;

if ($customerId) {
    $customer = getCustomer($customerId);
}

$errorMessages = [
    'card_save_failed' => 'Kart kaydetme işlemi başarısız oldu.',
    'save_failed' => 'Kart bilgileri kaydedilemedi.',
    'unknown' => 'Bilinmeyen bir hata oluştu.'
];

$errorMessage = $errorMessages[$error] ?? $errorMessages['unknown'];
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kart Kaydetme Başarısız - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .result-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
        }
        .result-icon {
            font-size: 4rem;
            color: #ff6b6b;
            margin-bottom: 2rem;
        }
        .btn-retry {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 50px;
        }
        .btn-support {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 50px;
        }
    </style>
</head>
<body>
    <div class="result-card">
        <i class="fas fa-exclamation-triangle result-icon"></i>
        <h2 class="text-danger mb-3">Kart Kaydetme Başarısız</h2>
        
        <?php if ($customer): ?>
            <div class="alert alert-warning">
                <strong><?php echo htmlspecialchars($customer['name']); ?></strong>, 
                kartınız kaydedilemedi.
            </div>
        <?php endif; ?>
        
        <p class="text-muted mb-4">
            <?php echo htmlspecialchars($errorMessage); ?>
            Lütfen tekrar deneyiniz veya farklı bir kart kullanınız.
        </p>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>Olası Nedenler:</strong>
            <ul class="mb-0 mt-2 text-start">
                <li>Kart bilgileri hatalı girilmiş olabilir</li>
                <li>Kartınız internet alışverişine kapalı olabilir</li>
                <li>Kartınızın son kullanma tarihi geçmiş olabilir</li>
                <li>Geçici bir teknik sorun yaşanmış olabilir</li>
                <li>Banka tarafından işlem reddedilmiş olabilir</li>
            </ul>
        </div>
        
        <div class="alert alert-warning">
            <i class="fas fa-lightbulb"></i>
            <strong>Öneriler:</strong>
            <ul class="mb-0 mt-2 text-start">
                <li>Kart bilgilerinizi kontrol ederek tekrar deneyin</li>
                <li>Farklı bir kart ile deneyiniz</li>
                <li>Bankanızla iletişime geçerek kartınızın durumunu kontrol edin</li>
                <li>İnternet alışverişi limitinizi kontrol edin</li>
            </ul>
        </div>
        
        <div class="d-flex justify-content-center gap-3 mt-4">
            <?php if ($customerId): ?>
                <a href="../save_card.php?customer_id=<?php echo $customerId; ?>" class="btn btn-success btn-retry">
                    <i class="fas fa-redo"></i> Tekrar Dene
                </a>
            <?php endif; ?>
            
            <button class="btn btn-secondary" onclick="window.close()">
                <i class="fas fa-times"></i> Kapat
            </button>
        </div>
        
        <div class="mt-4">
            <div class="row text-center">
                <div class="col-4">
                    <i class="fas fa-phone fa-2x text-primary mb-2"></i>
                    <small class="d-block text-muted">Telefon Desteği</small>
                </div>
                <div class="col-4">
                    <i class="fas fa-envelope fa-2x text-success mb-2"></i>
                    <small class="d-block text-muted">E-posta Desteği</small>
                </div>
                <div class="col-4">
                    <i class="fas fa-comments fa-2x text-warning mb-2"></i>
                    <small class="d-block text-muted">Canlı Destek</small>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-headset"></i> 
                Sorun devam ederse destek ekibimizle iletişime geçin: 
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </small>
        </div>
        
        <div class="mt-3">
            <small class="text-muted">
                <i class="fas fa-clock"></i>
                Destek ekibimiz 7/24 hizmetinizdedir.
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
