<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/paytr.php';

// Webhook güvenlik kontrolü
function validateWebhookSecurity() {
    // Content-Type kontrolü
    $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
    if (strpos($contentType, 'application/x-www-form-urlencoded') === false) {
        http_response_code(400);
        die('Invalid content type');
    }
    
    // User-Agent kontrolü
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    if (strpos($userAgent, 'PayTR') === false && !PAYTR_TEST_MODE) {
        http_response_code(403);
        die('Invalid user agent');
    }
    
    // Request method kontrolü
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        die('Method not allowed');
    }
    
    // Payload size kontrolü
    $contentLength = $_SERVER['CONTENT_LENGTH'] ?? 0;
    if ($contentLength > 10240) { // 10KB limit
        http_response_code(413);
        die('Payload too large');
    }
    
    return true;
}

// Webhook rate limiting
function checkWebhookRateLimit() {
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    $cacheKey = 'webhook_rate_' . md5($clientIP);
    
    $attempts = $_SESSION[$cacheKey] ?? [];
    $currentTime = time();
    
    // 1 dakikadan eski denemeleri temizle
    $attempts = array_filter($attempts, function($attempt) use ($currentTime) {
        return ($currentTime - $attempt) < 60;
    });
    
    if (count($attempts) >= 10) { // 1 dakikada en fazla 10 webhook
        http_response_code(429);
        die('Rate limit exceeded');
    }
    
    $attempts[] = $currentTime;
    $_SESSION[$cacheKey] = $attempts;
    
    return true;
}

// Webhook işleme
function processWebhook($data) {
    global $pdo;
    
    try {
        $webhookType = $data['webhook_type'] ?? 'payment';
        $merchant_oid = $data['merchant_oid'] ?? '';
        
        switch ($webhookType) {
            case 'payment':
                return processPaymentWebhook($data);
                
            case 'refund':
                return processRefundWebhook($data);
                
            case 'chargeback':
                return processChargebackWebhook($data);
                
            default:
                error_log("Unknown webhook type: $webhookType");
                return false;
        }
        
    } catch (Exception $e) {
        error_log("Webhook processing error: " . $e->getMessage());
        return false;
    }
}

// Ödeme webhook işleme
function processPaymentWebhook($data) {
    $merchant_oid = $data['merchant_oid'];
    $status = $data['status'];
    $amount = ($data['total_amount'] ?? 0) / 100;
    
    // Merchant OID'den bilgileri çıkar
    if (strpos($merchant_oid, 'SUB') === 0) {
        preg_match('/SUB(\d+)S(\d+)T(\d+)/', $merchant_oid, $matches);
        $customerId = $matches[1] ?? null;
        $subscriptionId = $matches[2] ?? null;
        
        if ($customerId && $subscriptionId) {
            if ($status === 'success') {
                // Başarılı ödeme
                addPayment([
                    'customer_id' => $customerId,
                    'subscription_id' => $subscriptionId,
                    'amount' => $amount,
                    'status' => 'success',
                    'paytr_transaction_id' => $merchant_oid
                ]);
                
                // Aboneliği aktif yap
                global $pdo;
                $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'active', next_payment_date = DATE_ADD(NOW(), INTERVAL 1 MONTH) WHERE customer_id = ? AND subscription_id = ?");
                $stmt->execute([$customerId, $subscriptionId]);
                
                return true;
            } else {
                // Başarısız ödeme
                addPayment([
                    'customer_id' => $customerId,
                    'subscription_id' => $subscriptionId,
                    'amount' => $amount,
                    'status' => 'failed',
                    'paytr_transaction_id' => $merchant_oid
                ]);
                
                return true;
            }
        }
    }
    
    return false;
}

// İade webhook işleme
function processRefundWebhook($data) {
    $merchant_oid = $data['merchant_oid'];
    $refund_amount = ($data['refund_amount'] ?? 0) / 100;
    
    // İade kaydı oluştur
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO refunds (merchant_oid, amount, status, created_at) VALUES (?, ?, 'completed', NOW())");
    return $stmt->execute([$merchant_oid, $refund_amount]);
}

// Chargeback webhook işleme
function processChargebackWebhook($data) {
    $merchant_oid = $data['merchant_oid'];
    $chargeback_amount = ($data['chargeback_amount'] ?? 0) / 100;
    
    // Chargeback kaydı oluştur ve aboneliği iptal et
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Chargeback kaydı
        $stmt = $pdo->prepare("INSERT INTO chargebacks (merchant_oid, amount, status, created_at) VALUES (?, ?, 'received', NOW())");
        $stmt->execute([$merchant_oid, $chargeback_amount]);
        
        // İlgili aboneliği iptal et
        if (strpos($merchant_oid, 'SUB') === 0) {
            preg_match('/SUB(\d+)S(\d+)T(\d+)/', $merchant_oid, $matches);
            $customerId = $matches[1] ?? null;
            $subscriptionId = $matches[2] ?? null;
            
            if ($customerId && $subscriptionId) {
                $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'cancelled', cancelled_at = NOW(), cancellation_reason = 'chargeback' WHERE customer_id = ? AND subscription_id = ?");
                $stmt->execute([$customerId, $subscriptionId]);
            }
        }
        
        $pdo->commit();
        return true;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Chargeback processing error: " . $e->getMessage());
        return false;
    }
}

// Ana webhook handler
try {
    // Güvenlik kontrolleri
    validateWebhookSecurity();
    checkWebhookRateLimit();
    
    // Webhook verilerini al
    $rawInput = file_get_contents('php://input');
    parse_str($rawInput, $webhookData);
    
    // PayTR callback doğrulaması
    if (!PayTR::verifyCallback($webhookData)) {
        error_log('Webhook hash verification failed');
        http_response_code(400);
        die('Invalid signature');
    }
    
    // Webhook'u işle
    $result = processWebhook($webhookData);
    
    if ($result) {
        // Webhook log kaydet
        addPayTRLog([
            'merchant_oid' => $webhookData['merchant_oid'] ?? '',
            'status' => $webhookData['status'] ?? '',
            'total_amount' => ($webhookData['total_amount'] ?? 0) / 100,
            'post_data' => $webhookData
        ]);
        
        http_response_code(200);
        echo "OK";
    } else {
        http_response_code(400);
        echo "Processing failed";
    }
    
} catch (Exception $e) {
    error_log("Webhook handler error: " . $e->getMessage());
    http_response_code(500);
    echo "Internal error";
}
?>
