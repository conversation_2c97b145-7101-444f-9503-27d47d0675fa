<?php
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

// API Authentication
function authenticateAPI() {
    $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? '';
    $validApiKey = hash('sha256', PAYTR_MERCHANT_KEY . PAYTR_MERCHANT_SALT);
    
    if (!hash_equals($validApiKey, $apiKey)) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized']);
        exit;
    }
}

// JSON response helper
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Input validation
function validateInput($data, $required = []) {
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            jsonResponse(['error' => "Missing required field: $field"], 400);
        }
    }
}

// Rate limiting
function checkAPIRateLimit() {
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    $cacheKey = 'api_rate_' . md5($clientIP);
    
    $attempts = $_SESSION[$cacheKey] ?? [];
    $currentTime = time();
    
    // 1 dakikadan eski denemeleri temizle
    $attempts = array_filter($attempts, function($attempt) use ($currentTime) {
        return ($currentTime - $attempt) < 60;
    });
    
    if (count($attempts) >= 30) { // 1 dakikada en fazla 30 istek
        jsonResponse(['error' => 'Rate limit exceeded'], 429);
    }
    
    $attempts[] = $currentTime;
    $_SESSION[$cacheKey] = $attempts;
}

// Ana API handler
try {
    // Güvenlik kontrolleri
    authenticateAPI();
    checkAPIRateLimit();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path = $_SERVER['PATH_INFO'] ?? '';
    $input = json_decode(file_get_contents('php://input'), true) ?? [];
    
    // Route handling
    switch ($method . ' ' . $path) {
        
        // Müşteri aboneliklerini getir
        case 'GET /customer/{id}/subscriptions':
            $customerId = $_GET['id'] ?? 0;
            if (!is_numeric($customerId)) {
                jsonResponse(['error' => 'Invalid customer ID'], 400);
            }
            
            $subscriptions = getCustomerActiveSubscriptions($customerId);
            jsonResponse(['subscriptions' => $subscriptions]);
            break;
            
        // Abonelik iptal et
        case 'POST /subscription/{id}/cancel':
            $subscriptionId = $_GET['id'] ?? 0;
            $reason = $input['reason'] ?? 'user_request';
            
            if (!is_numeric($subscriptionId)) {
                jsonResponse(['error' => 'Invalid subscription ID'], 400);
            }
            
            $result = cancelSubscription($subscriptionId, $reason);
            if ($result) {
                jsonResponse(['message' => 'Subscription cancelled successfully']);
            } else {
                jsonResponse(['error' => 'Failed to cancel subscription'], 500);
            }
            break;
            
        // Abonelik duraklat
        case 'POST /subscription/{id}/pause':
            $subscriptionId = $_GET['id'] ?? 0;
            $pauseUntil = $input['pause_until'] ?? null;
            
            if (!is_numeric($subscriptionId)) {
                jsonResponse(['error' => 'Invalid subscription ID'], 400);
            }
            
            $result = pauseSubscription($subscriptionId, $pauseUntil);
            if ($result) {
                jsonResponse(['message' => 'Subscription paused successfully']);
            } else {
                jsonResponse(['error' => 'Failed to pause subscription'], 500);
            }
            break;
            
        // Abonelik devam ettir
        case 'POST /subscription/{id}/resume':
            $subscriptionId = $_GET['id'] ?? 0;
            
            if (!is_numeric($subscriptionId)) {
                jsonResponse(['error' => 'Invalid subscription ID'], 400);
            }
            
            $result = resumeSubscription($subscriptionId);
            if ($result) {
                jsonResponse(['message' => 'Subscription resumed successfully']);
            } else {
                jsonResponse(['error' => 'Failed to resume subscription'], 500);
            }
            break;
            
        // Abonelik planı değiştir
        case 'POST /subscription/{id}/change-plan':
            $subscriptionId = $_GET['id'] ?? 0;
            validateInput($input, ['new_plan_id']);
            
            if (!is_numeric($subscriptionId)) {
                jsonResponse(['error' => 'Invalid subscription ID'], 400);
            }
            
            $newPlanId = $input['new_plan_id'];
            $prorationMode = $input['proration_mode'] ?? 'immediate';
            
            $result = changeSubscriptionPlan($subscriptionId, $newPlanId, $prorationMode);
            if (isset($result['error'])) {
                jsonResponse(['error' => $result['error']], 400);
            } else {
                jsonResponse($result);
            }
            break;
            
        // Ödeme geçmişi getir
        case 'GET /customer/{id}/payments':
            $customerId = $_GET['id'] ?? 0;
            $limit = $_GET['limit'] ?? 50;
            
            if (!is_numeric($customerId)) {
                jsonResponse(['error' => 'Invalid customer ID'], 400);
            }
            
            $payments = getPaymentHistory($customerId, $limit);
            jsonResponse(['payments' => $payments]);
            break;
            
        // Abonelik istatistikleri
        case 'GET /stats':
            $stats = getSubscriptionStats();
            jsonResponse(['stats' => $stats]);
            break;
            
        // Ödeme durumu sorgula
        case 'GET /payment/{merchant_oid}/status':
            $merchantOid = $_GET['merchant_oid'] ?? '';
            
            if (empty($merchantOid)) {
                jsonResponse(['error' => 'Missing merchant OID'], 400);
            }
            
            $result = PayTR::queryPayment($merchantOid);
            jsonResponse($result);
            break;
            
        // Kart sil
        case 'DELETE /card/{token}':
            $cardToken = $_GET['token'] ?? '';
            
            if (empty($cardToken)) {
                jsonResponse(['error' => 'Missing card token'], 400);
            }
            
            $result = PayTR::deleteCard($cardToken);
            jsonResponse($result);
            break;
            
        // İade işlemi
        case 'POST /payment/{merchant_oid}/refund':
            $merchantOid = $_GET['merchant_oid'] ?? '';
            $amount = $input['amount'] ?? null;
            
            if (empty($merchantOid)) {
                jsonResponse(['error' => 'Missing merchant OID'], 400);
            }
            
            $result = PayTR::refundPayment($merchantOid, $amount);
            jsonResponse($result);
            break;
            
        // Müşteri oluştur
        case 'POST /customer':
            validateInput($input, ['name', 'email', 'phone']);
            
            $result = addCustomer($input);
            if (is_array($result) && isset($result['error'])) {
                jsonResponse($result, 400);
            } else {
                jsonResponse(['customer' => $result], 201);
            }
            break;
            
        // Abonelik oluştur
        case 'POST /subscription':
            validateInput($input, ['customer_id', 'subscription_id']);
            
            $result = addCustomerSubscription($input['customer_id'], $input['subscription_id']);
            if ($result) {
                jsonResponse(['subscription' => $result], 201);
            } else {
                jsonResponse(['error' => 'Failed to create subscription'], 500);
            }
            break;
            
        default:
            jsonResponse(['error' => 'Endpoint not found'], 404);
    }
    
} catch (Exception $e) {
    error_log("API error: " . $e->getMessage());
    jsonResponse(['error' => 'Internal server error'], 500);
}
?>
