<?php
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

$customerId = $_GET['customer_id'] ?? null;
$customer = null;

if ($customerId) {
    $customer = getCustomer($customerId);
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kart <PERSON> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .result-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
        }
        .result-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 2rem;
        }
        .btn-continue {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 50px;
        }
        .success-animation {
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        .feature-list {
            text-align: left;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
    </style>
</head>
<body>
    <div class="result-card">
        <i class="fas fa-check-circle result-icon success-animation"></i>
        <h2 class="text-success mb-3">Kartınız Başarıyla Kaydedildi!</h2>
        
        <?php if ($customer): ?>
            <div class="alert alert-success">
                <strong><?php echo htmlspecialchars($customer['name']); ?></strong>, 
                kartınız güvenli bir şekilde kaydedilmiştir.
            </div>
        <?php endif; ?>
        
        <p class="text-muted mb-4">
            Artık aylık ödemeleriniz otomatik olarak bu karttan çekilecektir. 
            Herhangi bir işlem yapmanıza gerek yoktur.
        </p>
        
        <div class="feature-list">
            <h5><i class="fas fa-star text-warning"></i> Avantajlarınız</h5>
            <ul class="mb-0">
                <li><i class="fas fa-check text-success"></i> Aylık ödemeler otomatik çekilir</li>
                <li><i class="fas fa-check text-success"></i> Aboneliğiniz kesintisiz devam eder</li>
                <li><i class="fas fa-check text-success"></i> Her ay ödeme yapmak zorunda kalmazsınız</li>
                <li><i class="fas fa-check text-success"></i> Güvenli PayTR altyapısı ile korunur</li>
                <li><i class="fas fa-check text-success"></i> İstediğiniz zaman iptal edebilirsiniz</li>
            </ul>
        </div>
        
        <div class="alert alert-info">
            <i class="fas fa-calendar-alt"></i>
            <strong>Sonraki Ödeme:</strong> 
            Bir sonraki ödemeniz aboneliğinizin başlangıç tarihinden itibaren 1 ay sonra otomatik olarak çekilecektir.
        </div>
        
        <div class="row mt-4 text-center">
            <div class="col-4">
                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                <small class="d-block text-muted">Güvenli</small>
            </div>
            <div class="col-4">
                <i class="fas fa-sync-alt fa-2x text-primary mb-2"></i>
                <small class="d-block text-muted">Otomatik</small>
            </div>
            <div class="col-4">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <small class="d-block text-muted">Zamanında</small>
            </div>
        </div>
        
        <div class="mt-4">
            <p class="text-muted mb-3">
                <i class="fas fa-envelope"></i>
                Her ödeme sonrasında e-posta ile bilgilendirileceksiniz.
            </p>
            
            <div class="d-flex justify-content-center gap-3">
                <button class="btn btn-outline-secondary" onclick="window.close()">
                    <i class="fas fa-times"></i> Kapat
                </button>
            </div>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-headset"></i> 
                Sorularınız için: 
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Confetti animasyonu -->
    <script>
        // Basit confetti efekti
        function createConfetti() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
            
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'fixed';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.zIndex = '1000';
                confetti.style.borderRadius = '50%';
                confetti.style.pointerEvents = 'none';
                
                document.body.appendChild(confetti);
                
                const animation = confetti.animate([
                    { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
                    { transform: 'translateY(100vh) rotate(360deg)', opacity: 0 }
                ], {
                    duration: Math.random() * 3000 + 2000,
                    easing: 'cubic-bezier(0.5, 0, 0.5, 1)'
                });
                
                animation.onfinish = () => confetti.remove();
            }
        }
        
        // Sayfa yüklendiğinde confetti başlat
        window.addEventListener('load', createConfetti);
    </script>
</body>
</html>
