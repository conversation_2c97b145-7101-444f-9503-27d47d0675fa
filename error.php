<?php
$errorCode = $_GET['code'] ?? '500';
$errorMessage = $_GET['message'] ?? 'Bilinmeyen bir hata oluştu.';

// Güvenlik için error message'ı sanitize et
$errorMessage = htmlspecialchars($errorMessage, ENT_QUOTES, 'UTF-8');

$errorTitles = [
    '400' => 'Hatalı İstek',
    '403' => '<PERSON><PERSON>şim Engellendi',
    '404' => 'Sayfa Bulunamadı',
    '500' => 'Sunucu Hatası'
];

$errorTitle = $errorTitles[$errorCode] ?? 'Hata';

http_response_code((int)$errorCode);
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $errorTitle; ?> - PayTR Abonelik Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
        }
        .error-icon {
            font-size: 4rem;
            color: #ff6b6b;
            margin-bottom: 2rem;
        }
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            color: #ff6b6b;
            line-height: 1;
        }
    </style>
</head>
<body>
    <div class="error-card">
        <div class="error-code"><?php echo $errorCode; ?></div>
        <i class="fas fa-exclamation-triangle error-icon"></i>
        <h2 class="text-danger mb-3"><?php echo $errorTitle; ?></h2>
        <p class="text-muted mb-4"><?php echo $errorMessage; ?></p>
        
        <div class="d-flex justify-content-center gap-3">
            <button class="btn btn-primary" onclick="history.back()">
                <i class="fas fa-arrow-left"></i> Geri Dön
            </button>
            <a href="/" class="btn btn-outline-primary">
                <i class="fas fa-home"></i> Ana Sayfa
            </a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-headset"></i> 
                Sorun devam ederse: 
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
