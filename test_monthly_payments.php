<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/paytr.php';

// Test için monthly payments scriptini çalıştır

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Payments Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .console {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2><i class="fas fa-calendar-check"></i> Monthly Payments Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> Mevcut Abonelikler</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            global $pdo;
                            
                            // Tüm aktif abonelikleri göster
                            $stmt = $pdo->prepare("
                                SELECT cs.*, c.name as customer_name, c.email as customer_email,
                                       s.name as subscription_name, s.price as subscription_price
                                FROM customer_subscriptions cs
                                JOIN customers c ON cs.customer_id = c.id
                                JOIN subscriptions s ON cs.subscription_id = s.id
                                WHERE cs.status = 'active'
                                ORDER BY cs.next_payment_date ASC
                            ");
                            $stmt->execute();
                            $subscriptions = $stmt->fetchAll();
                            
                            if (empty($subscriptions)) {
                                echo '<div class="alert alert-warning">Aktif abonelik bulunamadı.</div>';
                            } else {
                                echo '<div class="table-responsive">';
                                echo '<table class="table table-sm">';
                                echo '<thead><tr><th>Müşteri</th><th>Plan</th><th>Sonraki Ödeme</th><th>Tutar</th></tr></thead>';
                                echo '<tbody>';
                                foreach ($subscriptions as $sub) {
                                    $nextPayment = date('d.m.Y', strtotime($sub['next_payment_date']));
                                    $isToday = $sub['next_payment_date'] === date('Y-m-d');
                                    $rowClass = $isToday ? 'table-warning' : '';
                                    
                                    echo "<tr class='$rowClass'>";
                                    echo "<td>" . htmlspecialchars($sub['customer_name']) . "</td>";
                                    echo "<td>" . htmlspecialchars($sub['subscription_name']) . "</td>";
                                    echo "<td>$nextPayment" . ($isToday ? ' <span class="badge bg-warning">BUGÜN</span>' : '') . "</td>";
                                    echo "<td>" . number_format($sub['subscription_price'], 2) . " ₺</td>";
                                    echo "</tr>";
                                }
                                echo '</tbody></table>';
                                echo '</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">Hata: ' . $e->getMessage() . '</div>';
                        }
                        ?>
                    </div>
                </div>
                
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-credit-card"></i> Kayıtlı Kartlar</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $stmt = $pdo->prepare("
                                SELECT pc.*, c.name as customer_name
                                FROM payment_cards pc
                                JOIN customers c ON pc.customer_id = c.id
                                ORDER BY pc.created_at DESC
                            ");
                            $stmt->execute();
                            $cards = $stmt->fetchAll();
                            
                            if (empty($cards)) {
                                echo '<div class="alert alert-warning">Kayıtlı kart bulunamadı.</div>';
                            } else {
                                echo '<div class="table-responsive">';
                                echo '<table class="table table-sm">';
                                echo '<thead><tr><th>Müşteri</th><th>Kart</th><th>Tarih</th></tr></thead>';
                                echo '<tbody>';
                                foreach ($cards as $card) {
                                    echo "<tr>";
                                    echo "<td>" . htmlspecialchars($card['customer_name']) . "</td>";
                                    echo "<td>**** **** **** " . htmlspecialchars($card['card_last4'] ?? '****') . "</td>";
                                    echo "<td>" . date('d.m.Y', strtotime($card['created_at'])) . "</td>";
                                    echo "</tr>";
                                }
                                echo '</tbody></table>';
                                echo '</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">Hata: ' . $e->getMessage() . '</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h5><i class="fas fa-terminal"></i> Test Konsolu</h5>
                        <div>
                            <button class="btn btn-sm btn-primary" onclick="runTest('today')">Bugün</button>
                            <button class="btn btn-sm btn-warning" onclick="runTest('all')">Tümü</button>
                            <button class="btn btn-sm btn-success" onclick="runTest('force')">Zorla Çalıştır</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="console" class="console">
                            Test seçin ve çalıştırın...
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Test Araçları</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="createTestSubscription()">
                                <i class="fas fa-plus"></i> Test Aboneliği Oluştur
                            </button>
                            <button class="btn btn-outline-success" onclick="addTestCards()">
                                <i class="fas fa-credit-card"></i> Test Kartları Ekle
                            </button>
                            <button class="btn btn-outline-warning" onclick="updatePaymentDates()">
                                <i class="fas fa-calendar"></i> Ödeme Tarihlerini Bugüne Çek
                            </button>
                            <button class="btn btn-outline-info" onclick="showLogs()">
                                <i class="fas fa-list"></i> Payment Logları
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="admin/" class="btn btn-secondary">← Admin Panel</a>
            <a href="debug_logs.php" class="btn btn-info">Debug Logs</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            console.innerHTML += `[${timestamp}] ${message}\n`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '';
        }
        
        function runTest(type) {
            clearConsole();
            log('Test başlatılıyor: ' + type);
            
            fetch('test_monthly_payments_api.php?type=' + type)
                .then(response => response.text())
                .then(data => {
                    log(data);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
        
        function createTestSubscription() {
            clearConsole();
            log('Test aboneliği oluşturuluyor...');

            fetch('test_monthly_payments_api.php?action=create_test')
                .then(response => response.text())
                .then(data => {
                    log(data);
                    setTimeout(() => location.reload(), 2000);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }

        function addTestCards() {
            clearConsole();
            log('Test kartları ekleniyor...');

            fetch('test_monthly_payments_api.php?action=add_test_cards')
                .then(response => response.text())
                .then(data => {
                    log(data);
                    setTimeout(() => location.reload(), 2000);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
        
        function updatePaymentDates() {
            clearConsole();
            log('Ödeme tarihleri güncelleniyor...');
            
            fetch('test_monthly_payments_api.php?action=update_dates')
                .then(response => response.text())
                .then(data => {
                    log(data);
                    setTimeout(() => location.reload(), 2000);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
        
        function showLogs() {
            clearConsole();
            log('Payment logları getiriliyor...');
            
            fetch('test_monthly_payments_api.php?action=show_logs')
                .then(response => response.text())
                .then(data => {
                    log(data);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
    </script>
</body>
</html>
