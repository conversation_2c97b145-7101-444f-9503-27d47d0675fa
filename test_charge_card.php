<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/paytr.php';

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Charge Card Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .console {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>PayTR Charge Card Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Kartları</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            global $pdo;
                            $stmt = $pdo->prepare("
                                SELECT pc.*, c.name as customer_name, c.id as customer_id
                                FROM payment_cards pc
                                JOIN customers c ON pc.customer_id = c.id
                                WHERE pc.is_active = 1
                                ORDER BY pc.created_at DESC
                                LIMIT 5
                            ");
                            $stmt->execute();
                            $cards = $stmt->fetchAll();
                            
                            if (empty($cards)) {
                                echo '<div class="alert alert-warning">Test kartı bulunamadı. <a href="test_monthly_payments.php">Test kartı ekleyin</a>.</div>';
                            } else {
                                foreach ($cards as $card) {
                                    echo '<div class="card mb-2">';
                                    echo '<div class="card-body">';
                                    echo '<h6>' . htmlspecialchars($card['customer_name']) . '</h6>';
                                    echo '<p class="mb-2">Kart: ' . htmlspecialchars($card['card_mask'] ?? '**** **** **** ****') . '</p>';
                                    echo '<p class="mb-2">Token: ' . htmlspecialchars(substr($card['card_token'], 0, 20)) . '...</p>';
                                    echo '<button class="btn btn-sm btn-primary" onclick="testChargeCard(\'' . $card['card_token'] . '\', ' . $card['customer_id'] . ', 1, 10.00)">';
                                    echo 'Test Charge (10 ₺)';
                                    echo '</button>';
                                    echo '</div>';
                                    echo '</div>';
                                }
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">Hata: ' . $e->getMessage() . '</div>';
                        }
                        ?>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Manuel Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Card Token</label>
                            <input type="text" class="form-control" id="cardToken" placeholder="TEST_CARD_TOKEN_...">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Customer ID</label>
                            <input type="number" class="form-control" id="customerId" value="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Subscription ID</label>
                            <input type="number" class="form-control" id="subscriptionId" value="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Amount (₺)</label>
                            <input type="number" class="form-control" id="amount" value="29.99" step="0.01">
                        </div>
                        <button class="btn btn-warning" onclick="manualChargeTest()">
                            Manuel Test
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Konsolu</h5>
                    </div>
                    <div class="card-body">
                        <div id="console" class="console">
                            Test kartı seçin ve charge test edin...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="test_monthly_payments.php" class="btn btn-secondary">← Test Araçları</a>
            <a href="debug_logs.php" class="btn btn-info">Debug Logs</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            console.innerHTML += `[${timestamp}] ${message}\n`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '';
        }
        
        function testChargeCard(cardToken, customerId, subscriptionId, amount) {
            clearConsole();
            log('Charge Card Test başlatılıyor...');
            log('Card Token: ' + cardToken.substring(0, 20) + '...');
            log('Customer ID: ' + customerId);
            log('Subscription ID: ' + subscriptionId);
            log('Amount: ' + amount + ' ₺');
            log('');
            
            const params = new URLSearchParams({
                action: 'charge_card',
                card_token: cardToken,
                customer_id: customerId,
                subscription_id: subscriptionId,
                amount: amount
            });
            
            fetch('test_charge_card_api.php?' + params)
                .then(response => response.text())
                .then(data => {
                    log('=== API YANITI ===');
                    log(data);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
        
        function manualChargeTest() {
            const cardToken = document.getElementById('cardToken').value;
            const customerId = document.getElementById('customerId').value;
            const subscriptionId = document.getElementById('subscriptionId').value;
            const amount = document.getElementById('amount').value;
            
            if (!cardToken) {
                alert('Card Token gerekli!');
                return;
            }
            
            testChargeCard(cardToken, customerId, subscriptionId, amount);
        }
    </script>
</body>
</html>
