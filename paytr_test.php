<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/paytr.php';

// Bu dosya sadece test amaçlı - production'da silinmeli!

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

echo "<h2>PayTR Konfigürasyon Test</h2>";

// Konfigürasyon kontrolü
echo "<h3>1. Konfigürasyon Kontrolü</h3>";
echo "Merchant ID: " . (PAYTR_MERCHANT_ID !== 'YOUR_MERCHANT_ID' ? '✓ Ayarlanmış' : '✗ Ayarlanmamış') . "<br>";
echo "Merchant Key: " . (PAYTR_MERCHANT_KEY !== 'YOUR_MERCHANT_KEY' ? '✓ Ayarlanmış' : '✗ Ayarlanmamış') . "<br>";
echo "Merchant Salt: " . (PAYTR_MERCHANT_SALT !== 'YOUR_MERCHANT_SALT' ? '✓ Ayarlanmış' : '✗ Ayarlanmamış') . "<br>";
echo "Test Mode: " . (PAYTR_TEST_MODE ? '✓ Aktif' : '✗ Pasif') . "<br>";
echo "Site URL: " . SITE_URL . "<br><br>";

// Test müşteri bilgileri
$testCustomer = [
    'id' => 999,
    'name' => 'Test Müşteri',
    'email' => '<EMAIL>',
    'phone' => '05551234567'
];

$testSubscriptionId = 1;
$testAmount = 29.99;

echo "<h3>2. Token Oluşturma Test</h3>";

try {
    // Test ödeme formu oluştur
    $paymentForm = PayTR::createPaymentForm($testCustomer['id'], $testSubscriptionId, $testAmount, $testCustomer);
    
    if (isset($paymentForm['status']) && $paymentForm['status'] === 'success') {
        echo "<div style='color: green;'>✓ PayTR Token başarıyla alındı!</div>";
        echo "<strong>Token:</strong> " . substr($paymentForm['token'], 0, 20) . "...<br><br>";

        echo "<strong>Form Verileri:</strong><br>";
        echo "<pre>";
        foreach ($paymentForm['form_data'] as $key => $value) {
            if ($key === 'paytr_token') {
                echo "$key: " . substr($value, 0, 20) . "... (kısaltıldı)<br>";
            } else {
                echo "$key: $value<br>";
            }
        }
        echo "</pre>";
    } else {
        echo "<div style='color: red;'>✗ PayTR Hatası: " . ($paymentForm['error'] ?? 'Bilinmeyen hata') . "</div>";

        if (isset($paymentForm['form_data'])) {
            echo "<strong>Gönderilen Form Verileri:</strong><br>";
            echo "<pre>";
            foreach ($paymentForm['form_data'] as $key => $value) {
                if ($key === 'paytr_token') {
                    echo "$key: " . substr($value, 0, 20) . "... (kısaltıldı)<br>";
                } else {
                    echo "$key: $value<br>";
                }
            }
            echo "</pre>";
        }

        // Yaygın hata çözümleri
        echo "<h4>Olası Çözümler:</h4>";
        echo "- payment_type parametresi eklendi<br>";
        echo "- Merchant ID, Key ve Salt bilgilerini kontrol edin<br>";
        echo "- Hash hesaplama sırasını kontrol edin<br>";
        echo "- Debug loglarını kontrol edin: <a href='debug_logs.php'>debug_logs.php</a><br>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Hata: " . $e->getMessage() . "</div>";
}

echo "<br><h3>4. Hash Hesaplama Detayları</h3>";

// Manuel hash hesaplama
$merchant_id = PAYTR_MERCHANT_ID;
$user_ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
$merchant_oid = "TEST" . time();
$email = $testCustomer['email'];
$payment_amount = $testAmount * 100;
$user_basket = base64_encode(json_encode([["Test Ürün", number_format($testAmount, 2), 1]]));
$no_installment = "1";
$max_installment = "0";
$currency = "TL";
$test_mode = "1";

$paytr_token_string = $merchant_id . $user_ip . $merchant_oid . $email . $payment_amount . $user_basket . $no_installment . $max_installment . $currency . $test_mode;

echo "<strong>Hash Hesaplama Detayları:</strong><br>";
echo "merchant_id: $merchant_id<br>";
echo "user_ip: $user_ip<br>";
echo "merchant_oid: $merchant_oid<br>";
echo "email: $email<br>";
echo "payment_amount: $payment_amount<br>";
echo "user_basket: $user_basket<br>";
echo "no_installment: $no_installment<br>";
echo "max_installment: $max_installment<br>";
echo "currency: $currency<br>";
echo "test_mode: $test_mode<br><br>";

echo "<strong>Birleştirilmiş String:</strong><br>";
echo "<code>$paytr_token_string</code><br><br>";

$token = base64_encode(hash_hmac('sha256', $paytr_token_string . PAYTR_MERCHANT_SALT, PAYTR_MERCHANT_KEY, true));
echo "<strong>Oluşturulan Token:</strong><br>";
echo "<code>$token</code><br><br>";

echo "<div style='background: #f0f0f0; padding: 10px; margin: 20px 0;'>";
echo "<strong>Not:</strong> Bu test sayfasını production ortamında kullanmayın ve test tamamlandıktan sonra silin!";
echo "</div>";

echo "<a href='admin/'>← Admin Paneline Dön</a>";
?>
