<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/paytr.php';

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

header('Content-Type: text/plain; charset=utf-8');

$type = $_GET['type'] ?? '';
$action = $_GET['action'] ?? '';

global $pdo;

if ($action === 'create_test') {
    // Test aboneliği oluştur
    echo "Test aboneliği oluşturuluyor...\n";
    
    try {
        // Test müşterisi oluştur
        $stmt = $pdo->prepare("INSERT INTO customers (name, email, phone, status) VALUES (?, ?, ?, 'active')");
        $stmt->execute(['Test Müşteri ' . time(), 'test' . time() . '@example.com', '05551234567']);
        $customerId = $pdo->lastInsertId();
        
        // Test abonelik planı al
        $stmt = $pdo->query("SELECT id FROM subscriptions WHERE status = 'active' LIMIT 1");
        $subscription = $stmt->fetch();
        
        if (!$subscription) {
            echo "HATA: Aktif abonelik planı bulunamadı!\n";
            exit;
        }
        
        // Test aboneliği oluştur (bugün ödemesi yapılacak)
        $stmt = $pdo->prepare("INSERT INTO customer_subscriptions (customer_id, subscription_id, status, next_payment_date) VALUES (?, ?, 'active', ?)");
        $stmt->execute([$customerId, $subscription['id'], date('Y-m-d')]);
        
        // Test kartı da ekle
        $stmt = $pdo->prepare("INSERT INTO payment_cards (customer_id, card_token, card_mask, card_type, is_active) VALUES (?, ?, ?, ?, 1)");
        $stmt->execute([$customerId, 'TEST_CARD_TOKEN_' . time(), '**** **** **** 1234', 'visa']);

        echo "✓ Test müşterisi oluşturuldu (ID: $customerId)\n";
        echo "✓ Test aboneliği oluşturuldu\n";
        echo "✓ Test kartı eklendi\n";
        echo "✓ Ödeme tarihi bugüne ayarlandı\n";
        
    } catch (Exception $e) {
        echo "HATA: " . $e->getMessage() . "\n";
    }
    
} elseif ($action === 'add_test_cards') {
    // Mevcut müşterilere test kartları ekle
    echo "Test kartları ekleniyor...\n";

    try {
        // Kartı olmayan müşterileri bul
        $stmt = $pdo->query("
            SELECT c.id, c.name
            FROM customers c
            LEFT JOIN payment_cards pc ON c.id = pc.customer_id AND pc.is_active = 1
            WHERE pc.id IS NULL AND c.status = 'active'
        ");
        $customersWithoutCards = $stmt->fetchAll();

        $addedCount = 0;
        foreach ($customersWithoutCards as $customer) {
            $stmt = $pdo->prepare("INSERT INTO payment_cards (customer_id, card_token, card_mask, card_type, is_active) VALUES (?, ?, ?, ?, 1)");
            $stmt->execute([
                $customer['id'],
                'TEST_CARD_TOKEN_' . $customer['id'] . '_' . time(),
                '**** **** **** ' . str_pad($customer['id'], 4, '0', STR_PAD_LEFT),
                'visa'
            ]);
            $addedCount++;
            echo "✓ " . $customer['name'] . " için test kartı eklendi\n";
        }

        echo "✓ Toplam $addedCount müşteriye test kartı eklendi\n";

    } catch (Exception $e) {
        echo "HATA: " . $e->getMessage() . "\n";
    }

} elseif ($action === 'update_dates') {
    // Mevcut aboneliklerin ödeme tarihlerini bugüne çek
    echo "Ödeme tarihleri güncelleniyor...\n";
    
    try {
        $stmt = $pdo->prepare("UPDATE customer_subscriptions SET next_payment_date = ? WHERE status = 'active'");
        $stmt->execute([date('Y-m-d')]);
        $affected = $stmt->rowCount();
        
        echo "✓ $affected aboneliğin ödeme tarihi bugüne çekildi\n";
        
    } catch (Exception $e) {
        echo "HATA: " . $e->getMessage() . "\n";
    }
    
} elseif ($action === 'show_logs') {
    // Payment loglarını göster
    echo "Son payment logları:\n";
    echo "==================\n";
    
    try {
        $stmt = $pdo->query("
            SELECT p.*, c.name as customer_name, s.name as subscription_name 
            FROM payments p 
            LEFT JOIN customers c ON p.customer_id = c.id 
            LEFT JOIN subscriptions s ON p.subscription_id = s.id 
            ORDER BY p.created_at DESC 
            LIMIT 10
        ");
        $payments = $stmt->fetchAll();
        
        if (empty($payments)) {
            echo "Henüz ödeme kaydı yok.\n";
        } else {
            foreach ($payments as $payment) {
                $status = $payment['status'] === 'success' ? '✓' : '✗';
                $date = date('d.m.Y H:i', strtotime($payment['created_at']));
                echo "$status $date - " . ($payment['customer_name'] ?? 'Bilinmeyen') . " - " . number_format($payment['amount'], 2) . " ₺ - " . strtoupper($payment['status']) . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "HATA: " . $e->getMessage() . "\n";
    }
    
} else {
    // Monthly payments scriptini çalıştır
    echo "Monthly Payments Script Test\n";
    echo "============================\n";
    echo "Tarih: " . date('Y-m-d H:i:s') . "\n";
    echo "Test Modu: " . ($type === 'force' ? 'ZORLA' : ($type === 'all' ? 'TÜMÜ' : 'BUGÜN')) . "\n\n";
    
    try {
        // Abonelikleri getir
        if ($type === 'all') {
            // Tüm aktif abonelikler
            $stmt = $pdo->prepare("
                SELECT cs.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
                       s.name as subscription_name, s.price as subscription_price
                FROM customer_subscriptions cs
                JOIN customers c ON cs.customer_id = c.id
                JOIN subscriptions s ON cs.subscription_id = s.id
                WHERE cs.status = 'active'
            ");
            $stmt->execute();
        } elseif ($type === 'force') {
            // Zorla tüm aktif abonelikler (tarih fark etmez)
            $stmt = $pdo->prepare("
                SELECT cs.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
                       s.name as subscription_name, s.price as subscription_price
                FROM customer_subscriptions cs
                JOIN customers c ON cs.customer_id = c.id
                JOIN subscriptions s ON cs.subscription_id = s.id
                WHERE cs.status = 'active'
            ");
            $stmt->execute();
        } else {
            // Sadece bugün ödemesi yapılacaklar
            $today = date('Y-m-d');
            $stmt = $pdo->prepare("
                SELECT cs.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
                       s.name as subscription_name, s.price as subscription_price
                FROM customer_subscriptions cs
                JOIN customers c ON cs.customer_id = c.id
                JOIN subscriptions s ON cs.subscription_id = s.id
                WHERE cs.status = 'active' AND cs.next_payment_date = ?
            ");
            $stmt->execute([$today]);
        }
        
        $customerSubscriptions = $stmt->fetchAll();
        
        echo "Bulunan abonelik sayısı: " . count($customerSubscriptions) . "\n\n";
        
        if (empty($customerSubscriptions)) {
            echo "İşlenecek abonelik bulunamadı.\n";
            
            if ($type === 'today') {
                echo "\nÖneriler:\n";
                echo "- 'Tümü' butonuna basarak tüm abonelikleri görebilirsiniz\n";
                echo "- 'Test Aboneliği Oluştur' ile yeni test aboneliği oluşturabilirsiniz\n";
                echo "- 'Ödeme Tarihlerini Bugüne Çek' ile mevcut abonelikleri test edebilirsiniz\n";
            }
            exit;
        }
        
        $processedCount = 0;
        $successCount = 0;
        $failedCount = 0;
        
        foreach ($customerSubscriptions as $customerSub) {
            $processedCount++;
            
            echo "[$processedCount] İşleniyor: " . $customerSub['customer_name'] . " - " . $customerSub['subscription_name'] . " - " . number_format($customerSub['subscription_price'], 2) . " ₺\n";
            
            // Kayıtlı kart kontrolü
            $customerCard = getCustomerCard($customerSub['customer_id']);
            
            if (!$customerCard) {
                echo "   ✗ Kayıtlı kart bulunamadı\n";
                
                // Test modunda gerçek işlem yapmayalım
                if ($type !== 'force') {
                    echo "   → Test modunda abonelik askıya alınmadı\n";
                } else {
                    $stmt = $pdo->prepare("UPDATE customer_subscriptions SET status = 'suspended' WHERE id = ?");
                    $stmt->execute([$customerSub['id']]);
                    echo "   → Abonelik askıya alındı\n";
                }
                
                $failedCount++;
                continue;
            }
            
            echo "   ✓ Kayıtlı kart bulundu: " . ($customerCard['card_last4'] ?? '****') . "\n";
            
            // Test modunda PayTR çağrısı yapmayalım
            if ($type === 'force') {
                echo "   → PayTR ile ödeme çekiliyor...\n";
                
                // Gerçek PayTR çağrısı (dikkatli olun!)
                $result = PayTR::chargeCard(
                    $customerCard['card_token'],
                    $customerSub['customer_id'],
                    $customerSub['subscription_id'],
                    $customerSub['subscription_price']
                );
                
                if ($result && isset($result['status']) && $result['status'] === 'success') {
                    echo "   ✓ Ödeme başarılı!\n";
                    $successCount++;
                } else {
                    echo "   ✗ Ödeme başarısız: " . ($result['error_message'] ?? 'Bilinmeyen hata') . "\n";
                    $failedCount++;
                }
            } else {
                echo "   → Test modunda PayTR çağrısı yapılmadı\n";
                echo "   → Simüle edilen sonuç: BAŞARILI\n";
                $successCount++;
            }
            
            echo "\n";
        }
        
        echo "=== ÖZET ===\n";
        echo "Toplam: $processedCount\n";
        echo "Başarılı: $successCount\n";
        echo "Başarısız: $failedCount\n";
        echo "Tamamlandı: " . date('H:i:s') . "\n";
        
    } catch (Exception $e) {
        echo "HATA: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
}
?>
