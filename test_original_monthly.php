<?php
// Orijinal monthly payments API'sini test et
require_once 'includes/config.php';

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orijinal Monthly Payments Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .console {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Orijinal Monthly Payments API Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Seçenekleri</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="runOriginalAPI()">
                                <i class="fas fa-play"></i> Orijinal API'yi Çalıştır
                            </button>
                            <button class="btn btn-warning" onclick="runOriginalAPIManual()">
                                <i class="fas fa-cog"></i> Manuel Çalıştır (?manual_run=1)
                            </button>
                            <hr>
                            <a href="test_monthly_payments.php" class="btn btn-info">
                                <i class="fas fa-tools"></i> Test Araçları
                            </a>
                            <a href="api/monthly_payments.php?manual_run=1" target="_blank" class="btn btn-success">
                                <i class="fas fa-external-link-alt"></i> Direkt API Çağrısı
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API Çıktısı</h5>
                    </div>
                    <div class="card-body">
                        <div id="console" class="console">
                            Test seçin ve çalıştırın...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="admin/" class="btn btn-secondary">← Admin Panel</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        function log(message) {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            console.innerHTML += `[${timestamp}] ${message}\n`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '';
        }
        
        function runOriginalAPI() {
            clearConsole();
            log('Orijinal API çalıştırılıyor...');
            
            fetch('api/monthly_payments.php')
                .then(response => response.text())
                .then(data => {
                    log('=== API ÇIKTISI ===');
                    log(data);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
        
        function runOriginalAPIManual() {
            clearConsole();
            log('Orijinal API manuel çalıştırılıyor...');
            
            fetch('api/monthly_payments.php?manual_run=1')
                .then(response => response.text())
                .then(data => {
                    log('=== API ÇIKTISI ===');
                    log(data);
                })
                .catch(error => {
                    log('HATA: ' + error);
                });
        }
    </script>
</body>
</html>
