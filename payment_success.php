<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// URL parametrelerinden bilgileri al
$merchant_oid = $_GET['merchant_oid'] ?? '';
$status = $_GET['status'] ?? 'success'; // Bu sayfaya geliyorsa başarılıdır
$total_amount = $_GET['total_amount'] ?? '';

// Bu sayfaya yönlendirme yapılıyorsa ödeme başarılıdır
$isValidHash = true; // Hash kontrolü gereksiz
$amount = $total_amount ? $total_amount / 100 : 0;

// Merchant OID'den bilgileri çıkar
$customerInfo = null;
$subscriptionInfo = null;

if (strpos($merchant_oid, 'SUB') === 0) {
    preg_match('/SUB(\d+)S(\d+)T(\d+)/', $merchant_oid, $matches);
    $customerId = $matches[1] ?? null;
    $subscriptionId = $matches[2] ?? null;
    
    if ($customerId && $subscriptionId) {
        $customerInfo = getCustomer($customerId);
        $subscriptionInfo = getSubscription($subscriptionId);
    }
} elseif (strpos($merchant_oid, 'CARD') === 0) {
    preg_match('/CARD(\d+)T(\d+)/', $merchant_oid, $matches);
    $customerId = $matches[1] ?? null;
    
    if ($customerId) {
        $customerInfo = getCustomer($customerId);
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme Başarılı - PayTR Abonelik Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .success-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .success-card {
            background: white;
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
        }
        
        .success-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: pulse 3s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }
        
        .success-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }
        
        .success-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 2;
        }
        
        .success-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .success-body {
            padding: 3rem 2rem;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 5px solid #28a745;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
        }
        
        .info-value {
            font-weight: bold;
            color: #495057;
        }
        
        .amount-highlight {
            font-size: 1.5rem;
            color: #28a745;
            font-weight: bold;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .confetti {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }
        
        .security-badges {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #dee2e6;
        }
        
        .security-badge {
            text-align: center;
            color: #6c757d;
        }
        
        .security-badge i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #28a745;
        }
        
        @media (max-width: 768px) {
            .success-header {
                padding: 2rem 1rem;
            }
            
            .success-title {
                font-size: 2rem;
            }
            
            .success-body {
                padding: 2rem 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .security-badges {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="confetti" id="confetti"></div>
    
    <div class="success-container">
        <div class="success-card animate__animated animate__bounceIn">
            <!-- Başarılı Ödeme -->
                <div class="success-header">
                    <div class="success-icon animate__animated animate__bounceIn animate__delay-1s">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h1 class="success-title animate__animated animate__fadeInUp animate__delay-2s">
                        Teşekkürler!
                    </h1>
                    <p class="success-subtitle animate__animated animate__fadeInUp animate__delay-3s">
                        Ödemeniz başarıyla alındı
                    </p>
                </div>
                
                <div class="success-body">
                    <div class="info-card animate__animated animate__fadeInUp animate__delay-4s">

                        
                        <?php if ($amount > 0): ?>
                        <div class="info-row">
                            <span class="info-label">
                                <i class="fas fa-money-bill-wave me-2"></i>Ödenen Tutar
                            </span>
                            <span class="info-value amount-highlight">
                                <?php echo number_format($amount, 2); ?> ₺
                            </span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($customerInfo): ?>
                        <div class="info-row">
                            <span class="info-label">
                                <i class="fas fa-user me-2"></i>Müşteri
                            </span>
                            <span class="info-value"><?php echo htmlspecialchars($customerInfo['name']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($subscriptionInfo): ?>
                        <div class="info-row">
                            <span class="info-label">
                                <i class="fas fa-star me-2"></i>Abonelik Planı
                            </span>
                            <span class="info-value"><?php echo htmlspecialchars($subscriptionInfo['name']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="info-row">
                            <span class="info-label">
                                <i class="fas fa-calendar me-2"></i>İşlem Tarihi
                            </span>
                            <span class="info-value"><?php echo date('d.m.Y H:i'); ?></span>
                        </div>
                    </div>
                    
                    <?php if (strpos($merchant_oid, 'SUB') === 0): ?>
                        <!-- Abonelik Ödemesi Mesajı -->
                        <div class="alert alert-success animate__animated animate__fadeInUp animate__delay-5s">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Aboneliğiniz aktif!</strong> 
                            Bir sonraki ödeme tarihiniz otomatik olarak belirlenecek ve kartınızdan çekilecektir.
                        </div>
                    <?php elseif (strpos($merchant_oid, 'CARD') === 0): ?>
                        <!-- Kart Kaydetme Mesajı -->
                        <div class="alert alert-info animate__animated animate__fadeInUp animate__delay-5s">
                            <i class="fas fa-credit-card me-2"></i>
                            <strong>Kartınız başarıyla kaydedildi!</strong> 
                            Artık abonelik ödemeleriniz otomatik olarak bu karttan çekilecektir.
                        </div>
                    <?php endif; ?>
                    
                    <div class="action-buttons animate__animated animate__fadeInUp animate__delay-6s">

                        <a href="/" class="btn btn-primary btn-custom">
                            <i class="fas fa-home me-2"></i>Ana Sayfa
                        </a>
                    </div>
                    
                    <div class="security-badges animate__animated animate__fadeInUp animate__delay-7s">
                        <div class="security-badge">
                            <i class="fas fa-shield-alt"></i>
                            <div><small>SSL Güvenlik</small></div>
                        </div>
                        <div class="security-badge">
                            <i class="fas fa-lock"></i>
                            <div><small>256-bit Şifreleme</small></div>
                        </div>
                        <div class="security-badge">
                            <i class="fas fa-check-double"></i>
                            <div><small>PayTR Güvencesi</small></div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Confetti animasyonu
        function createConfetti() {
            const confettiContainer = document.getElementById('confetti');
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
            
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'absolute';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                confetti.style.animationDelay = Math.random() * 2 + 's';
                confetti.style.animation = 'fall linear infinite';
                confettiContainer.appendChild(confetti);
            }
        }
        
        // CSS animasyonu ekle
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                0% {
                    transform: translateY(-100vh) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Sayfa yüklendiğinde confetti başlat
        window.addEventListener('load', function() {
            createConfetti();

            // 5 saniye sonra confetti'yi temizle
            setTimeout(function() {
                document.getElementById('confetti').innerHTML = '';
            }, 5000);
        });
    </script>
</body>
</html>
