<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

$action = $_GET['action'] ?? '';

if ($action === 'recent_payments') {
    // Son ödemeler
    try {
        global $pdo;
        $stmt = $pdo->query("
            SELECT p.*, c.name as customer_name 
            FROM payments p 
            LEFT JOIN customers c ON p.customer_id = c.id 
            ORDER BY p.created_at DESC 
            LIMIT 5
        ");
        $payments = $stmt->fetchAll();
        
        if (empty($payments)) {
            echo '<div class="alert alert-info">Henüz ödeme kaydı yok.</div>';
        } else {
            echo '<div class="table-responsive">';
            echo '<table class="table table-sm">';
            echo '<thead><tr><th>Müşteri</th><th>Tutar</th><th>Durum</th><th><PERSON>rih</th></tr></thead>';
            echo '<tbody>';
            foreach ($payments as $payment) {
                $statusClass = $payment['status'] === 'success' ? 'success' : 'danger';
                $date = date('d.m H:i', strtotime($payment['created_at']));
                echo "<tr>";
                echo "<td>" . htmlspecialchars($payment['customer_name'] ?? 'Bilinmeyen') . "</td>";
                echo "<td>" . number_format($payment['amount'], 2) . " ₺</td>";
                echo "<td><span class='badge bg-$statusClass'>" . strtoupper($payment['status']) . "</span></td>";
                echo "<td>$date</td>";
                echo "</tr>";
            }
            echo '</tbody></table>';
            echo '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Hata: ' . $e->getMessage() . '</div>';
    }
    exit;
}

if ($action === 'recent_cards') {
    // Son kartlar
    try {
        global $pdo;
        $stmt = $pdo->query("
            SELECT pc.*, c.name as customer_name 
            FROM payment_cards pc 
            LEFT JOIN customers c ON pc.customer_id = c.id 
            WHERE pc.is_active = 1
            ORDER BY pc.created_at DESC 
            LIMIT 5
        ");
        $cards = $stmt->fetchAll();
        
        if (empty($cards)) {
            echo '<div class="alert alert-info">Henüz kart kaydı yok.</div>';
        } else {
            echo '<div class="table-responsive">';
            echo '<table class="table table-sm">';
            echo '<thead><tr><th>Müşteri</th><th>Kart</th><th>Tip</th><th>Tarih</th></tr></thead>';
            echo '<tbody>';
            foreach ($cards as $card) {
                $date = date('d.m H:i', strtotime($card['created_at']));
                echo "<tr>";
                echo "<td>" . htmlspecialchars($card['customer_name'] ?? 'Bilinmeyen') . "</td>";
                echo "<td>" . htmlspecialchars($card['card_mask'] ?? '**** **** **** ****') . "</td>";
                echo "<td>" . strtoupper($card['card_type'] ?? 'unknown') . "</td>";
                echo "<td>$date</td>";
                echo "</tr>";
            }
            echo '</tbody></table>';
            echo '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Hata: ' . $e->getMessage() . '</div>';
    }
    exit;
}

// Callback test
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: text/plain; charset=utf-8');
    
    $merchant_oid = $_POST['merchant_oid'] ?? '';
    $status = $_POST['status'] ?? '';
    $total_amount = $_POST['total_amount'] ?? '';
    $card_token = $_POST['card_token'] ?? '';
    $card_mask = $_POST['card_mask'] ?? '';
    $card_type = $_POST['card_type'] ?? '';
    
    echo "PayTR Callback Test\n";
    echo "==================\n";
    echo "Merchant OID: $merchant_oid\n";
    echo "Status: $status\n";
    echo "Total Amount: $total_amount kuruş\n";
    
    if ($card_token) {
        echo "Card Token: " . substr($card_token, 0, 20) . "...\n";
        echo "Card Mask: $card_mask\n";
        echo "Card Type: $card_type\n";
    }
    
    echo "\n";
    
    if (empty($merchant_oid) || empty($status) || empty($total_amount)) {
        echo "HATA: Eksik parametreler!\n";
        exit;
    }
    
    // Hash hesapla (PayTR formatında)
    $merchant_key = PAYTR_MERCHANT_KEY;
    $merchant_salt = PAYTR_MERCHANT_SALT;
    $hash = base64_encode(hash_hmac('sha256', $merchant_oid . $merchant_salt . $status . $total_amount, $merchant_key, true));
    
    echo "Hash hesaplandı: " . substr($hash, 0, 20) . "...\n\n";
    
    // POST verilerini hazırla
    $postData = [
        'merchant_oid' => $merchant_oid,
        'status' => $status,
        'total_amount' => $total_amount,
        'hash' => $hash
    ];
    
    if ($card_token) {
        $postData['card_token'] = $card_token;
        $postData['card_mask'] = $card_mask;
        $postData['card_type'] = $card_type;
    }
    
    echo "Callback gönderiliyor...\n";
    
    // Callback'i simüle et
    $_POST = $postData;
    $_SERVER['REMOTE_ADDR'] = '127.0.0.1'; // Test IP
    
    // Session başlat (rate limiting için)
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Callback dosyasını include et
    ob_start();
    try {
        include 'api/paytr_success.php';
        $callbackResponse = ob_get_contents();
    } catch (Exception $e) {
        $callbackResponse = "HATA: " . $e->getMessage();
    }
    ob_end_clean();
    
    echo "Callback yanıtı: $callbackResponse\n";
    
    // Veritabanı değişikliklerini kontrol et
    echo "\n=== VERİTABANI KONTROL ===\n";
    
    try {
        global $pdo;
        
        // Son ödeme kaydını kontrol et
        $stmt = $pdo->prepare("SELECT * FROM payments WHERE paytr_transaction_id = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$merchant_oid]);
        $payment = $stmt->fetch();
        
        if ($payment) {
            echo "✓ Ödeme kaydı oluşturuldu:\n";
            echo "  - ID: " . $payment['id'] . "\n";
            echo "  - Customer ID: " . $payment['customer_id'] . "\n";
            echo "  - Amount: " . $payment['amount'] . " ₺\n";
            echo "  - Status: " . $payment['status'] . "\n";
        } else {
            echo "✗ Ödeme kaydı bulunamadı\n";
        }
        
        // Kart kaydını kontrol et (eğer kart token varsa)
        if ($card_token && $status === 'success') {
            $stmt = $pdo->prepare("SELECT * FROM payment_cards WHERE card_token = ? ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([$card_token]);
            $card = $stmt->fetch();
            
            if ($card) {
                echo "✓ Kart kaydı oluşturuldu:\n";
                echo "  - ID: " . $card['id'] . "\n";
                echo "  - Customer ID: " . $card['customer_id'] . "\n";
                echo "  - Card Mask: " . $card['card_mask'] . "\n";
                echo "  - Card Type: " . $card['card_type'] . "\n";
            } else {
                echo "✗ Kart kaydı bulunamadı\n";
            }
        }
        
        // PayTR log kontrol et
        $stmt = $pdo->prepare("SELECT * FROM paytr_logs WHERE merchant_oid = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$merchant_oid]);
        $log = $stmt->fetch();
        
        if ($log) {
            echo "✓ PayTR log kaydı oluşturuldu:\n";
            echo "  - ID: " . $log['id'] . "\n";
            echo "  - Status: " . $log['status'] . "\n";
        } else {
            echo "✗ PayTR log kaydı bulunamadı\n";
        }
        
    } catch (Exception $e) {
        echo "HATA: Veritabanı kontrolü başarısız - " . $e->getMessage() . "\n";
    }
    
    echo "\nTest tamamlandı!\n";
}
?>
