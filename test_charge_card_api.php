<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/paytr.php';

if (!PAYTR_TEST_MODE) {
    die('Bu test sadece test modunda çalışır!');
}

header('Content-Type: text/plain; charset=utf-8');

$action = $_GET['action'] ?? '';

if ($action === 'charge_card') {
    $cardToken = $_GET['card_token'] ?? '';
    $customerId = $_GET['customer_id'] ?? '';
    $subscriptionId = $_GET['subscription_id'] ?? '';
    $amount = floatval($_GET['amount'] ?? 0);
    
    echo "PayTR Charge Card Test\n";
    echo "=====================\n";
    echo "Tarih: " . date('Y-m-d H:i:s') . "\n";
    echo "Card Token: " . substr($cardToken, 0, 20) . "...\n";
    echo "Customer ID: $customerId\n";
    echo "Subscription ID: $subscriptionId\n";
    echo "Amount: $amount ₺\n\n";
    
    if (empty($cardToken) || empty($customerId) || empty($subscriptionId) || $amount <= 0) {
        echo "HATA: Eksik parametreler!\n";
        exit;
    }
    
    try {
        echo "PayTR chargeCard fonksiyonu çağrılıyor...\n";
        
        $result = PayTR::chargeCard($cardToken, $customerId, $subscriptionId, $amount);
        
        echo "\n=== PAYTR YANITI ===\n";
        echo "Status: " . ($result['status'] ?? 'unknown') . "\n";
        
        if (isset($result['status'])) {
            if ($result['status'] === 'success') {
                echo "✓ BAŞARILI!\n";
                echo "Transaction ID: " . ($result['transaction_id'] ?? 'N/A') . "\n";
                echo "Merchant OID: " . ($result['merchant_oid'] ?? 'N/A') . "\n";
                echo "Amount: " . ($result['amount'] ?? 'N/A') . "\n";
            } else {
                echo "✗ BAŞARISIZ!\n";
                echo "Error: " . ($result['error_message'] ?? $result['reason'] ?? 'Bilinmeyen hata') . "\n";
                
                if (isset($result['error_code'])) {
                    echo "Error Code: " . $result['error_code'] . "\n";
                }
            }
        } else {
            echo "✗ GEÇERSIZ YANIT!\n";
            echo "Raw Response: " . print_r($result, true) . "\n";
        }
        
        echo "\n=== DEBUG BİLGİLERİ ===\n";
        echo "Debug logları için debug_logs.php sayfasını kontrol edin.\n";
        
    } catch (Exception $e) {
        echo "\n=== EXCEPTION ===\n";
        echo "HATA: " . $e->getMessage() . "\n";
        echo "Stack Trace: " . $e->getTraceAsString() . "\n";
    }
    
} else {
    echo "Geçersiz action: $action\n";
}
?>
