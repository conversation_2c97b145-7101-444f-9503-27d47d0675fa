<?php
// PayTR Abonelik Sistemi Kurulum Dosyası

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Adım 1: Veritabanı bağlantı bilgileri
if ($_POST && $step == 1) {
    $dbHost = $_POST['db_host'] ?? 'localhost';
    $dbName = $_POST['db_name'] ?? 'paytr_abonelik';
    $dbUser = $_POST['db_user'] ?? 'root';
    $dbPass = $_POST['db_pass'] ?? '';
    
    try {
        $dsn = "mysql:host=$dbHost;charset=utf8mb4";
        $pdo = new PDO($dsn, $dbUser, $dbPass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        // Veritabanını oluştur
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$dbName`");
        
        // SQL dosyasını oku ve çalıştır
        $sql = file_get_contents('database/schema.sql');
        $sql = str_replace('paytr_abonelik', $dbName, $sql);
        
        // SQL komutlarını ayır ve çalıştır
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        foreach ($statements as $statement) {
            if (!empty($statement) && !str_starts_with($statement, '--')) {
                $pdo->exec($statement);
            }
        }
        
        // Config dosyasını güncelle
        $configContent = file_get_contents('includes/config.php');
        $configContent = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$dbHost');", $configContent);
        $configContent = str_replace("define('DB_NAME', 'paytr_abonelik');", "define('DB_NAME', '$dbName');", $configContent);
        $configContent = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$dbUser');", $configContent);
        $configContent = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$dbPass');", $configContent);
        
        file_put_contents('includes/config.php', $configContent);
        
        $success = 'Veritabanı başarıyla kuruldu!';
        $step = 2;
        
    } catch (Exception $e) {
        $error = 'Veritabanı kurulum hatası: ' . $e->getMessage();
    }
}

// Adım 2: PayTR ayarları
if ($_POST && $step == 2) {
    $merchantId = $_POST['merchant_id'] ?? '';
    $merchantKey = $_POST['merchant_key'] ?? '';
    $merchantSalt = $_POST['merchant_salt'] ?? '';
    $siteUrl = $_POST['site_url'] ?? '';
    
    if ($merchantId && $merchantKey && $merchantSalt && $siteUrl) {
        $configContent = file_get_contents('includes/config.php');
        $configContent = str_replace("define('PAYTR_MERCHANT_ID', 'YOUR_MERCHANT_ID');", "define('PAYTR_MERCHANT_ID', '$merchantId');", $configContent);
        $configContent = str_replace("define('PAYTR_MERCHANT_KEY', 'YOUR_MERCHANT_KEY');", "define('PAYTR_MERCHANT_KEY', '$merchantKey');", $configContent);
        $configContent = str_replace("define('PAYTR_MERCHANT_SALT', 'YOUR_MERCHANT_SALT');", "define('PAYTR_MERCHANT_SALT', '$merchantSalt');", $configContent);
        $configContent = str_replace("define('SITE_URL', 'http://localhost/int');", "define('SITE_URL', '$siteUrl');", $configContent);
        
        file_put_contents('includes/config.php', $configContent);
        
        $success = 'PayTR ayarları başarıyla kaydedildi!';
        $step = 3;
    } else {
        $error = 'Lütfen tüm alanları doldurun!';
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayTR Abonelik Sistemi - Kurulum</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .install-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step.pending {
            background: #e9ecef;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="install-card">
                    <div class="install-header">
                        <i class="fas fa-cogs fa-3x mb-3"></i>
                        <h2>PayTR Abonelik Sistemi</h2>
                        <p class="mb-0">Kurulum Sihirbazı</p>
                    </div>
                    
                    <div class="p-4">
                        <!-- Adım Göstergesi -->
                        <div class="step-indicator">
                            <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</div>
                            <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</div>
                            <div class="step <?php echo $step >= 3 ? 'completed' : 'pending'; ?>">3</div>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($step == 1): ?>
                            <!-- Adım 1: Veritabanı Ayarları -->
                            <h4><i class="fas fa-database"></i> Adım 1: Veritabanı Ayarları</h4>
                            <p class="text-muted">Veritabanı bağlantı bilgilerinizi girin:</p>
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="db_host" class="form-label">Veritabanı Sunucusu</label>
                                    <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                </div>
                                <div class="mb-3">
                                    <label for="db_name" class="form-label">Veritabanı Adı</label>
                                    <input type="text" class="form-control" id="db_name" name="db_name" value="paytr_abonelik" required>
                                </div>
                                <div class="mb-3">
                                    <label for="db_user" class="form-label">Kullanıcı Adı</label>
                                    <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                                </div>
                                <div class="mb-3">
                                    <label for="db_pass" class="form-label">Şifre</label>
                                    <input type="password" class="form-control" id="db_pass" name="db_pass">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> Devam Et
                                </button>
                            </form>
                            
                        <?php elseif ($step == 2): ?>
                            <!-- Adım 2: PayTR Ayarları -->
                            <h4><i class="fas fa-credit-card"></i> Adım 2: PayTR Ayarları</h4>
                            <p class="text-muted">PayTR hesap bilgilerinizi girin:</p>
                            
                            <form method="POST">
                                <input type="hidden" name="step" value="2">
                                <div class="mb-3">
                                    <label for="merchant_id" class="form-label">Merchant ID</label>
                                    <input type="text" class="form-control" id="merchant_id" name="merchant_id" required>
                                </div>
                                <div class="mb-3">
                                    <label for="merchant_key" class="form-label">Merchant Key</label>
                                    <input type="text" class="form-control" id="merchant_key" name="merchant_key" required>
                                </div>
                                <div class="mb-3">
                                    <label for="merchant_salt" class="form-label">Merchant Salt</label>
                                    <input type="text" class="form-control" id="merchant_salt" name="merchant_salt" required>
                                </div>
                                <div class="mb-3">
                                    <label for="site_url" class="form-label">Site URL</label>
                                    <input type="url" class="form-control" id="site_url" name="site_url" value="<?php echo 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>" required>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> Devam Et
                                </button>
                            </form>
                            
                        <?php elseif ($step == 3): ?>
                            <!-- Adım 3: Kurulum Tamamlandı -->
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                                <h4>Kurulum Tamamlandı!</h4>
                                <p class="text-muted mb-4">PayTR Abonelik Sistemi başarıyla kuruldu.</p>
                                
                                <div class="alert alert-info text-start">
                                    <h6><i class="fas fa-info-circle"></i> Önemli Bilgiler:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Admin Panel:</strong> <a href="admin/" target="_blank">admin/</a></li>
                                        <li><strong>Admin Şifresi:</strong> admin123</li>
                                        <li><strong>Cron Job:</strong> <code>0 9 * * * /usr/bin/php <?php echo __DIR__; ?>/api/monthly_payments.php</code></li>
                                    </ul>
                                </div>
                                
                                <div class="alert alert-warning text-start">
                                    <h6><i class="fas fa-exclamation-triangle"></i> Güvenlik:</h6>
                                    <ul class="mb-0">
                                        <li>Bu kurulum dosyasını silin: <code>install.php</code></li>
                                        <li>Admin şifresini değiştirin</li>
                                        <li>Veritabanı şifrenizi güçlü yapın</li>
                                    </ul>
                                </div>
                                
                                <div class="d-flex justify-content-center gap-3">
                                    <a href="admin/" class="btn btn-primary">
                                        <i class="fas fa-tachometer-alt"></i> Admin Panel
                                    </a>
                                    <button class="btn btn-danger" onclick="deleteInstallFile()">
                                        <i class="fas fa-trash"></i> Kurulum Dosyasını Sil
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteInstallFile() {
            if (confirm('Kurulum dosyasını silmek istediğinizden emin misiniz?')) {
                fetch('install.php?delete=1')
                    .then(() => {
                        alert('Kurulum dosyası silindi!');
                        window.location.href = 'admin/';
                    });
            }
        }
    </script>
</body>
</html>

<?php
// Kurulum dosyasını sil
if (isset($_GET['delete'])) {
    unlink(__FILE__);
    exit('OK');
}
?>
